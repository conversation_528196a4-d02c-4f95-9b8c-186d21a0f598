# iperf3-controller 服务端配置
# 服务器IP: *************

server:
  host: "0.0.0.0"
  port: 55201
  mode: "server"
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "60s"

iperf3_server:
  binary_path: "/usr/bin/iperf3"
  port: 55201
  dynamic_mode: true
  auto_shutdown_timeout: "60s"
  max_connections: 10

client_management:
  prepare_timeout: "10s"
  test_timeout: "60s"
  stop_timeout: "5s"
  retry_attempts: 3
  retry_delay: "2s"

database:
  type: "sqlite"
  path: "/var/lib/iperf3-controller/server-138-2-114-254.db"
  max_connections: 10
  connection_timeout: "5s"

logging:
  level: "info"
  file: "/var/log/iperf3-controller/server-138-2-114-254.log"
  max_size: "50MB"
  max_backups: 3
  max_age: 7
  compress: true

web:
  enabled: false

iperf3:
  binary_path: "/usr/bin/iperf3"
  default_duration: 30
  tcp:
    parallel_streams: 4
    window_size: "512K"
    mss: 1460
  udp:
    bandwidth: "500M"
    packet_size: 1472

monitoring:
  enable_performance_monitoring: true
  collection_interval: "1m"
  enable_health_check: true
  health_check_interval: "30s"

security:
  enable_https: false
  allowed_ips:
    - "0.0.0.0/0"

advanced:
  debug_mode: false
  enable_metrics: true
  metrics_port: 9090

server_info:
  id: "server-138-2-114-254"
  name: "外网测试服务器6"
  description: "用户真实外网测试服务器6 - *************"
  location: "外网节点6"
  tags:
    - "production"
    - "external"
    - "iperf3-server"
