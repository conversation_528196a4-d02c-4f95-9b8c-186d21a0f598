# iPerf3 双OpenWRT测速系统

## 🎯 项目概述

这是一个高性能的网络测速系统，采用双OpenWRT服务端架构，支持抢占式测试和实时数据同步。

### 核心特性

- **抢占式测试**: 所有服务器同时抢占，谁先响应谁先测速 🚀
- **双OpenWRT架构**: 奇数/偶数小时分别测试，负载均衡
- **实时数据同步**: 双OpenWRT SQLite完全同步
- **Web可视化**: 现代化Web界面显示实时速度
- **多平台支持**: 支持Linux、Windows、macOS、OpenWRT等平台

### 技术栈

- **后端**: Go 1.21+, Gin框架, SQLite数据库
- **前端**: 原生JavaScript, HTML5, CSS3
- **网络**: iPerf3, HTTP/WebSocket
- **部署**: Docker, 系统服务

## 🚀 快速开始

### 环境要求

- Go 1.21+
- SQLite3
- iPerf3
- Node.js (可选，用于前端构建)

### 安装步骤

1. **克隆项目**
   ```bash
   git clone https://github.com/your-repo/iperf3-controller.git
   cd iperf3-controller
   ```

2. **安装依赖**
   ```bash
   go mod tidy
   ```

3. **构建项目**
   ```bash
   # Windows
   .\build.ps1
   
   # Linux/macOS
   ./scripts/build.sh
   ```

4. **配置系统**
   ```bash
   cp config.example.yaml config.yaml
   # 编辑配置文件
   ```

5. **启动服务**
   ```bash
   ./iperf3-controller server -c config.yaml
   ```

## 📋 配置说明

### 基础配置

```yaml
server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: 30s
  write_timeout: 30s

database:
  path: "./iperf3.db"
  max_open_conns: 10
  max_idle_conns: 5

clients:
  - id: "server1"
    name: "测试服务器1"
    host: "*************"
    port: 55200
    enabled: true
  - id: "server2"
    name: "测试服务器2"
    host: "*************"
    port: 55201
    enabled: true

schedule:
  mode: "hourly"
  timezone: "Asia/Shanghai"
  odd_hours: true
  even_hours: true

sync:
  peer_host: "*************"
  peer_port: 8080
  sync_interval: 30s
```

### 高级配置

```yaml
# 抢占式测试配置
preemptive:
  enabled: true
  max_concurrent: 12
  timeout: 60s
  retry_attempts: 3

# 性能优化
performance:
  cache_size: 10000
  connection_pool: 20
  query_timeout: 10s

# 监控配置
monitoring:
  metrics_enabled: true
  health_check_interval: 30s
  log_level: "info"
```

## 🔧 使用指南

### 命令行工具

```bash
# 启动服务器
iperf3-controller server -c config.yaml

# 客户端模式
iperf3-controller client -c config.yaml

# 手动触发测试
iperf3-controller test --manual

# 查看状态
iperf3-controller status

# 查看帮助
iperf3-controller --help
```

### Web界面

访问 `http://localhost:8080` 查看Web界面：

- **仪表板**: 实时系统状态和性能指标
- **客户端管理**: 查看和管理测试客户端
- **测试控制**: 手动触发和监控测试
- **历史数据**: 查看历史测试结果
- **系统设置**: 配置系统参数

### API接口

#### 系统状态

```bash
# 获取系统状态
GET /api/v1/status

# 获取健康检查
GET /api/v1/health
```

#### 客户端管理

```bash
# 获取客户端列表
GET /api/v1/clients

# 添加客户端
POST /api/v1/clients
{
  "id": "server1",
  "name": "测试服务器1",
  "host": "*************",
  "port": 55200,
  "enabled": true
}

# 更新客户端
PUT /api/v1/clients/{id}

# 删除客户端
DELETE /api/v1/clients/{id}
```

#### 测试控制

```bash
# 触发测试
POST /api/v1/tests/trigger

# 获取当前测试
GET /api/v1/tests/current

# 取消当前测试
POST /api/v1/tests/cancel

# 获取测试历史
GET /api/v1/tests/history
```

#### 调度管理

```bash
# 启动调度器
POST /api/v1/schedule/start

# 停止调度器
POST /api/v1/schedule/stop

# 获取调度状态
GET /api/v1/schedule/status
```

#### 同步管理

```bash
# 启动同步
POST /api/v1/sync/start

# 停止同步
POST /api/v1/sync/stop

# 获取同步状态
GET /api/v1/sync/status
```

## 🏗️ 部署指南

### Docker部署

```bash
# 构建镜像
docker build -t iperf3-controller .

# 运行容器
docker run -d \
  --name iperf3-controller \
  -p 8080:8080 \
  -v /path/to/config:/app/config.yaml \
  -v /path/to/data:/app/data \
  iperf3-controller
```

### 系统服务部署

```bash
# 安装服务
sudo ./scripts/deploy.sh

# 启动服务
sudo systemctl start iperf3-controller

# 设置开机自启
sudo systemctl enable iperf3-controller

# 查看状态
sudo systemctl status iperf3-controller
```

### OpenWRT部署

```bash
# 上传文件到OpenWRT
scp iperf3-controller-openwrt-mips root@***********:/opt/
scp config.yaml root@***********:/etc/iperf3-controller/

# 在OpenWRT上安装
ssh root@***********
chmod +x /opt/iperf3-controller-openwrt-mips
/opt/iperf3-controller-openwrt-mips server -c /etc/iperf3-controller/config.yaml
```

## 🧪 测试指南

### 单元测试

```bash
# 运行所有测试
go test ./...

# 运行特定测试
go test ./internal/database

# 生成测试覆盖率报告
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

### 集成测试

```bash
# 运行集成测试
go run test_integration.go

# 运行API测试
go test ./test -run TestAPI
```

### 性能测试

```bash
# 运行基准测试
go test -bench=. ./...

# 运行压力测试
go test -bench=BenchmarkDatabasePerformance ./test
```

## 📊 监控和日志

### 日志配置

```yaml
logging:
  level: "info"
  format: "json"
  output: "stdout"
  file: "/var/log/iperf3-controller/app.log"
  max_size: 100MB
  max_age: 30d
  max_backups: 10
```

### 监控指标

- **系统指标**: CPU使用率、内存使用率、磁盘使用率
- **网络指标**: 带宽利用率、延迟、丢包率
- **应用指标**: 请求响应时间、错误率、并发数
- **业务指标**: 测试成功率、平均速度、峰值速度

### 告警配置

```yaml
alerts:
  cpu_threshold: 80%
  memory_threshold: 85%
  disk_threshold: 90%
  error_rate_threshold: 5%
  response_time_threshold: 5s
```

## 🔧 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查数据库文件权限
   ls -la iperf3.db
   
   # 重新初始化数据库
   rm iperf3.db
   ./iperf3-controller init
   ```

2. **客户端连接超时**
   ```bash
   # 检查网络连通性
   ping *************
   
   # 检查防火墙设置
   iptables -L
   ```

3. **Web界面无法访问**
   ```bash
   # 检查服务状态
   systemctl status iperf3-controller
   
   # 检查端口监听
   netstat -tlnp | grep 8080
   ```

### 调试模式

```bash
# 启用调试日志
export LOG_LEVEL=debug
./iperf3-controller server -c config.yaml

# 启用详细输出
./iperf3-controller server -c config.yaml --verbose
```

## 🤝 贡献指南

### 开发环境设置

```bash
# 安装开发依赖
go install golang.org/x/tools/cmd/goimports@latest
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest

# 代码格式化
goimports -w .

# 代码检查
golangci-lint run
```

### 提交规范

- 使用语义化提交信息
- 包含测试用例
- 更新相关文档
- 遵循代码规范

### 测试要求

- 单元测试覆盖率 > 80%
- 集成测试覆盖主要功能
- 性能测试验证关键路径

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [iPerf3](https://iperf.fr/) - 网络性能测试工具
- [Gin](https://github.com/gin-gonic/gin) - Web框架
- [SQLite](https://www.sqlite.org/) - 嵌入式数据库
- [OpenWRT](https://openwrt.org/) - Linux发行版

## 📞 联系方式

- 项目主页: https://github.com/your-repo/iperf3-controller
- 问题反馈: https://github.com/your-repo/iperf3-controller/issues
- 邮箱: <EMAIL>

---

**注意**: 这是一个生产就绪的网络测速系统，请在生产环境中使用前进行充分测试。
