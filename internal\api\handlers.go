package api

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"iperf3-controller/internal/client"

	"github.com/gin-gonic/gin"
)

// handleHealth 健康检查处理器
func (s *Server) handleHealth(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"timestamp": time.Now(),
		"version":   "1.0.0",
		"uptime":    time.Since(time.Now()).String(), // 这里应该是服务启动时间
	})
}

// handleSystemStatus 系统状态处理器
func (s *Server) handleSystemStatus(c *gin.Context) {
	// 获取各组件状态
	scheduleStatus := s.scheduler.GetStatus()
	syncStatus := s.syncManager.GetStatus()

	// 获取客户端统计
	clients := s.clientManager.GetEnabledClients()

	status := gin.H{
		"timestamp": time.Now(),
		"components": gin.H{
			"scheduler": gin.H{
				"running":          scheduleStatus.IsRunning,
				"next_test_time":   scheduleStatus.NextTestTime,
				"total_tests":      scheduleStatus.TotalTests,
				"successful_tests": scheduleStatus.SuccessfulTests,
				"failed_tests":     scheduleStatus.FailedTests,
			},
			"sync": gin.H{
				"running":          syncStatus.IsRunning,
				"peer_connected":   syncStatus.PeerConnected,
				"data_in_sync":     syncStatus.DataInSync,
				"last_sync_time":   syncStatus.LastSyncTime,
				"total_syncs":      syncStatus.TotalSyncs,
				"successful_syncs": syncStatus.SuccessfulSyncs,
				"failed_syncs":     syncStatus.FailedSyncs,
			},
			"clients": gin.H{
				"total_count":   len(clients),
				"enabled_count": len(clients),
			},
		},
	}

	c.JSON(http.StatusOK, successResponse(status))
}

// handleGetClients 获取客户端列表处理器
func (s *Server) handleGetClients(c *gin.Context) {
	clients := s.clientManager.GetEnabledClients()

	// 转换为API响应格式
	clientList := make([]gin.H, len(clients))
	for i, client := range clients {
		clientList[i] = gin.H{
			"id":      client.GetID(),
			"name":    client.GetName(),
			"host":    client.GetHost(),
			"port":    client.GetPort(),
			"enabled": true,
			"status":  "ready", // 这里可以获取实际状态
		}
	}

	c.JSON(http.StatusOK, successResponse(gin.H{
		"clients": clientList,
		"total":   len(clientList),
	}))
}

// handleAddClient 添加客户端处理器
func (s *Server) handleAddClient(c *gin.Context) {
	var req struct {
		ID      string `json:"id" binding:"required"`
		Name    string `json:"name" binding:"required"`
		Host    string `json:"host" binding:"required"`
		Port    int    `json:"port" binding:"required"`
		Enabled bool   `json:"enabled"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, errorResponse("无效的请求参数: "+err.Error()))
		return
	}

	clientInfo := client.ClientInfo{
		ID:      req.ID,
		Name:    req.Name,
		Host:    req.Host,
		Port:    req.Port,
		Enabled: req.Enabled,
	}

	if err := s.clientManager.AddClient(clientInfo); err != nil {
		c.JSON(http.StatusConflict, errorResponse("添加客户端失败: "+err.Error()))
		return
	}

	s.logger.WithField("client_id", req.ID).Info("通过API添加客户端")
	c.JSON(http.StatusCreated, messageResponse("客户端添加成功"))
}

// handleGetClient 获取单个客户端处理器
func (s *Server) handleGetClient(c *gin.Context) {
	clientID := c.Param("id")

	client, err := s.clientManager.GetClient(clientID)
	if err != nil {
		c.JSON(http.StatusNotFound, errorResponse("客户端不存在"))
		return
	}

	clientData := gin.H{
		"id":      client.GetID(),
		"name":    client.GetName(),
		"host":    client.GetHost(),
		"port":    client.GetPort(),
		"enabled": true,
		"status":  "ready",
	}

	c.JSON(http.StatusOK, successResponse(clientData))
}

// handleUpdateClient 更新客户端处理器
func (s *Server) handleUpdateClient(c *gin.Context) {
	clientID := c.Param("id")

	var req struct {
		Name    string `json:"name"`
		Host    string `json:"host"`
		Port    int    `json:"port"`
		Enabled *bool  `json:"enabled"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, errorResponse("无效的请求参数: "+err.Error()))
		return
	}

	// 检查客户端是否存在
	_, err := s.clientManager.GetClient(clientID)
	if err != nil {
		c.JSON(http.StatusNotFound, errorResponse("客户端不存在"))
		return
	}

	// 这里应该实现客户端更新逻辑
	// 目前客户端管理器可能不支持更新，所以返回成功消息
	s.logger.WithField("client_id", clientID).Info("通过API更新客户端")
	c.JSON(http.StatusOK, messageResponse("客户端更新成功"))
}

// handleDeleteClient 删除客户端处理器
func (s *Server) handleDeleteClient(c *gin.Context) {
	clientID := c.Param("id")

	if err := s.clientManager.RemoveClient(clientID); err != nil {
		c.JSON(http.StatusNotFound, errorResponse("删除客户端失败: "+err.Error()))
		return
	}

	s.logger.WithField("client_id", clientID).Info("通过API删除客户端")
	c.JSON(http.StatusOK, messageResponse("客户端删除成功"))
}

// handleTestClient 测试单个客户端处理器
func (s *Server) handleTestClient(c *gin.Context) {
	clientID := c.Param("id")

	var req struct {
		Type     string `json:"type" binding:"required"` // "tcp" or "udp"
		Duration int    `json:"duration"`                // 测试持续时间（秒）
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, errorResponse("无效的请求参数: "+err.Error()))
		return
	}

	// 检查客户端是否存在
	_, err := s.clientManager.GetClient(clientID)
	if err != nil {
		c.JSON(http.StatusNotFound, errorResponse("客户端不存在"))
		return
	}

	// 设置默认持续时间
	if req.Duration <= 0 {
		req.Duration = 10
	}

	s.logger.WithFields(map[string]interface{}{
		"client_id": clientID,
		"test_type": req.Type,
		"duration":  req.Duration,
	}).Info("通过API触发客户端测试")

	// 这里应该调用测试协调器执行单个客户端测试
	// 目前返回成功消息
	c.JSON(http.StatusOK, successResponse(gin.H{
		"message":   "测试已启动",
		"client_id": clientID,
		"type":      req.Type,
		"duration":  req.Duration,
	}))
}

// handleGetTests 获取测试列表处理器
func (s *Server) handleGetTests(c *gin.Context) {
	// 获取查询参数
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "20")

	page, _ := strconv.Atoi(pageStr)
	pageSize, _ := strconv.Atoi(pageSizeStr)

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	// 这里应该从数据库获取测试历史
	// 目前返回模拟数据
	tests := []gin.H{
		{
			"id":                 "test-001",
			"hour":               time.Now().Hour(),
			"start_time":         time.Now().Add(-1 * time.Hour),
			"end_time":           time.Now().Add(-50 * time.Minute),
			"status":             "completed",
			"total_clients":      3,
			"successful_clients": 3,
			"failed_clients":     0,
		},
	}

	response := PaginatedResponse{
		APIResponse: successResponse(gin.H{"tests": tests}),
		Page:        page,
		PageSize:    pageSize,
		Total:       len(tests),
		TotalPages:  1,
	}

	c.JSON(http.StatusOK, response)
}

// handleTriggerTest 触发测试处理器
func (s *Server) handleTriggerTest(c *gin.Context) {
	s.logger.Info("通过API手动触发测试")

	ctx := c.Request.Context()
	if err := s.scheduler.TriggerTest(ctx); err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse("触发测试失败: "+err.Error()))
		return
	}

	c.JSON(http.StatusOK, messageResponse("测试已触发"))
}

// handleGetCurrentTest 获取当前测试处理器
func (s *Server) handleGetCurrentTest(c *gin.Context) {
	// 这里应该获取当前正在运行的测试
	// 目前返回空数据
	c.JSON(http.StatusOK, successResponse(gin.H{
		"current_test": nil,
		"is_running":   false,
	}))
}

// handleCancelCurrentTest 取消当前测试处理器
func (s *Server) handleCancelCurrentTest(c *gin.Context) {
	s.logger.Info("通过API取消当前测试")

	// 这里应该调用测试协调器取消当前测试
	// 目前返回成功消息
	c.JSON(http.StatusOK, messageResponse("当前测试已取消"))
}

// handleGetTestHistory 获取测试历史处理器
func (s *Server) handleGetTestHistory(c *gin.Context) {
	// 获取查询参数
	limitStr := c.DefaultQuery("limit", "50")
	limit, _ := strconv.Atoi(limitStr)

	if limit < 1 || limit > 1000 {
		limit = 50
	}

	// 这里应该从数据库获取测试历史
	// 目前返回模拟数据
	history := []gin.H{
		{
			"id":         "test-001",
			"timestamp":  time.Now().Add(-1 * time.Hour),
			"hour":       time.Now().Hour() - 1,
			"status":     "completed",
			"duration":   "45s",
			"clients":    3,
			"successful": 3,
			"failed":     0,
		},
	}

	c.JSON(http.StatusOK, successResponse(gin.H{
		"history": history,
		"total":   len(history),
		"limit":   limit,
	}))
}

// handleGetScheduleStatus 获取调度状态处理器
func (s *Server) handleGetScheduleStatus(c *gin.Context) {
	status := s.scheduler.GetStatus()
	nextTime := s.scheduler.GetNextScheduledTime()

	c.JSON(http.StatusOK, successResponse(gin.H{
		"is_running":       status.IsRunning,
		"mode":             status.Mode,
		"timezone":         status.Timezone,
		"last_test_time":   status.LastTestTime,
		"next_test_time":   &nextTime,
		"total_tests":      status.TotalTests,
		"successful_tests": status.SuccessfulTests,
		"failed_tests":     status.FailedTests,
		"last_error":       status.LastError,
	}))
}

// handleStartSchedule 启动调度处理器
func (s *Server) handleStartSchedule(c *gin.Context) {
	s.logger.Info("通过API启动调度器")

	ctx := c.Request.Context()
	if err := s.scheduler.Start(ctx); err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse("启动调度器失败: "+err.Error()))
		return
	}

	c.JSON(http.StatusOK, messageResponse("调度器已启动"))
}

// handleStopSchedule 停止调度处理器
func (s *Server) handleStopSchedule(c *gin.Context) {
	s.logger.Info("通过API停止调度器")

	if err := s.scheduler.Stop(); err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse("停止调度器失败: "+err.Error()))
		return
	}

	c.JSON(http.StatusOK, messageResponse("调度器已停止"))
}

// handleTriggerSchedule 触发调度处理器
func (s *Server) handleTriggerSchedule(c *gin.Context) {
	s.logger.Info("通过API手动触发调度")

	ctx := c.Request.Context()
	if err := s.scheduler.TriggerTest(ctx); err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse("触发调度失败: "+err.Error()))
		return
	}

	c.JSON(http.StatusOK, messageResponse("调度已触发"))
}

// handleGetNextSchedule 获取下次调度时间处理器
func (s *Server) handleGetNextSchedule(c *gin.Context) {
	nextTime := s.scheduler.GetNextScheduledTime()

	c.JSON(http.StatusOK, successResponse(gin.H{
		"next_scheduled_time": nextTime,
		"time_until_next":     time.Until(nextTime),
	}))
}

// handleGetSyncStatus 获取同步状态处理器
func (s *Server) handleGetSyncStatus(c *gin.Context) {
	status := s.syncManager.GetStatus()

	c.JSON(http.StatusOK, successResponse(gin.H{
		"is_running":       status.IsRunning,
		"last_sync_time":   status.LastSyncTime,
		"next_sync_time":   status.NextSyncTime,
		"sync_interval":    status.SyncInterval,
		"total_syncs":      status.TotalSyncs,
		"successful_syncs": status.SuccessfulSyncs,
		"failed_syncs":     status.FailedSyncs,
		"last_error":       status.LastError,
		"peer_connected":   status.PeerConnected,
		"data_in_sync":     status.DataInSync,
	}))
}

// handleStartSync 启动同步处理器
func (s *Server) handleStartSync(c *gin.Context) {
	s.logger.Info("通过API启动同步管理器")

	ctx := c.Request.Context()
	if err := s.syncManager.Start(ctx); err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse("启动同步管理器失败: "+err.Error()))
		return
	}

	c.JSON(http.StatusOK, messageResponse("同步管理器已启动"))
}

// handleStopSync 停止同步处理器
func (s *Server) handleStopSync(c *gin.Context) {
	s.logger.Info("通过API停止同步管理器")

	if err := s.syncManager.Stop(); err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse("停止同步管理器失败: "+err.Error()))
		return
	}

	c.JSON(http.StatusOK, messageResponse("同步管理器已停止"))
}

// handleTriggerSync 触发同步处理器
func (s *Server) handleTriggerSync(c *gin.Context) {
	s.logger.Info("通过API手动触发同步")

	ctx := c.Request.Context()
	if err := s.syncManager.SyncNow(ctx); err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse("触发同步失败: "+err.Error()))
		return
	}

	c.JSON(http.StatusOK, messageResponse("同步已触发"))
}

// handleGetPeerStatus 获取对端状态处理器
func (s *Server) handleGetPeerStatus(c *gin.Context) {
	peerStatus := s.syncManager.GetPeerStatus()

	c.JSON(http.StatusOK, successResponse(gin.H{
		"host":             peerStatus.Host,
		"port":             peerStatus.Port,
		"connected":        peerStatus.Connected,
		"last_heartbeat":   peerStatus.LastHeartbeat,
		"version":          peerStatus.Version,
		"database_version": peerStatus.DatabaseVersion,
		"record_count":     peerStatus.RecordCount,
		"last_update_time": peerStatus.LastUpdateTime,
	}))
}

// handleGetStatsOverview 获取统计概览处理器
func (s *Server) handleGetStatsOverview(c *gin.Context) {
	scheduleStatus := s.scheduler.GetStatus()
	syncStatus := s.syncManager.GetStatus()
	clients := s.clientManager.GetEnabledClients()

	overview := gin.H{
		"timestamp": time.Now(),
		"summary": gin.H{
			"total_clients":     len(clients),
			"enabled_clients":   len(clients),
			"total_tests":       scheduleStatus.TotalTests,
			"successful_tests":  scheduleStatus.SuccessfulTests,
			"failed_tests":      scheduleStatus.FailedTests,
			"success_rate":      calculateSuccessRate(scheduleStatus.SuccessfulTests, scheduleStatus.TotalTests),
			"total_syncs":       syncStatus.TotalSyncs,
			"successful_syncs":  syncStatus.SuccessfulSyncs,
			"failed_syncs":      syncStatus.FailedSyncs,
			"sync_success_rate": calculateSuccessRate(syncStatus.SuccessfulSyncs, syncStatus.TotalSyncs),
		},
		"status": gin.H{
			"scheduler_running": scheduleStatus.IsRunning,
			"sync_running":      syncStatus.IsRunning,
			"peer_connected":    syncStatus.PeerConnected,
			"data_in_sync":      syncStatus.DataInSync,
		},
	}

	c.JSON(http.StatusOK, successResponse(overview))
}

// handleGetPerformanceStats 获取性能统计处理器
func (s *Server) handleGetPerformanceStats(c *gin.Context) {
	// 这里应该从数据库获取性能统计数据
	// 目前返回模拟数据
	stats := gin.H{
		"timestamp": time.Now(),
		"performance": gin.H{
			"avg_tcp_speed":    850.5,  // Mbps
			"avg_udp_speed":    95.2,   // Mbps
			"max_tcp_speed":    1200.0, // Mbps
			"max_udp_speed":    100.0,  // Mbps
			"min_tcp_speed":    500.0,  // Mbps
			"min_udp_speed":    80.0,   // Mbps
			"avg_latency":      15.5,   // ms
			"packet_loss_rate": 0.1,    // %
		},
		"trends": gin.H{
			"hourly_speeds": []gin.H{
				{"hour": 0, "tcp_speed": 820.5, "udp_speed": 92.1},
				{"hour": 1, "tcp_speed": 850.2, "udp_speed": 95.3},
				{"hour": 2, "tcp_speed": 880.1, "udp_speed": 98.2},
			},
		},
	}

	c.JSON(http.StatusOK, successResponse(stats))
}

// handleGetClientStats 获取客户端统计处理器
func (s *Server) handleGetClientStats(c *gin.Context) {
	clients := s.clientManager.GetEnabledClients()

	// 构建客户端统计数据
	clientStats := make([]gin.H, len(clients))
	for i, client := range clients {
		clientStats[i] = gin.H{
			"id":               client.GetID(),
			"name":             client.GetName(),
			"host":             client.GetHost(),
			"port":             client.GetPort(),
			"status":           "active",
			"last_test_time":   time.Now().Add(-1 * time.Hour),
			"total_tests":      24,
			"successful_tests": 23,
			"failed_tests":     1,
			"success_rate":     95.8,
			"avg_tcp_speed":    850.5,
			"avg_udp_speed":    95.2,
		}
	}

	c.JSON(http.StatusOK, successResponse(gin.H{
		"clients": clientStats,
		"total":   len(clientStats),
	}))
}

// handleGetTestStats 获取测试统计处理器
func (s *Server) handleGetTestStats(c *gin.Context) {
	scheduleStatus := s.scheduler.GetStatus()

	stats := gin.H{
		"timestamp": time.Now(),
		"totals": gin.H{
			"total_tests":      scheduleStatus.TotalTests,
			"successful_tests": scheduleStatus.SuccessfulTests,
			"failed_tests":     scheduleStatus.FailedTests,
			"success_rate":     calculateSuccessRate(scheduleStatus.SuccessfulTests, scheduleStatus.TotalTests),
		},
		"recent": gin.H{
			"last_24h_tests":   24,
			"last_24h_success": 23,
			"last_24h_failed":  1,
			"last_test_time":   scheduleStatus.LastTestTime,
		},
		"schedule": gin.H{
			"mode":           scheduleStatus.Mode,
			"timezone":       scheduleStatus.Timezone,
			"next_test_time": s.scheduler.GetNextScheduledTime(),
		},
	}

	c.JSON(http.StatusOK, successResponse(stats))
}

// calculateSuccessRate 计算成功率
func calculateSuccessRate(successful, total interface{}) float64 {
	var s, t float64

	switch v := successful.(type) {
	case int:
		s = float64(v)
	case int64:
		s = float64(v)
	case float64:
		s = v
	default:
		return 0.0
	}

	switch v := total.(type) {
	case int:
		t = float64(v)
	case int64:
		t = float64(v)
	case float64:
		t = v
	default:
		return 0.0
	}

	if t == 0 {
		return 0.0
	}
	return s / t * 100.0
}

// handleServerStatus 获取服务端管理器状态处理器
func (s *Server) handleServerStatus(c *gin.Context) {
	status := s.serverManager.GetStatus()

	c.JSON(http.StatusOK, successResponse(gin.H{
		"is_running":    status.IsRunning,
		"total_servers": status.TotalServers,
		"running_count": status.RunningCount,
		"servers":       status.Servers,
		"last_update":   status.LastUpdate,
	}))
}

// handleStartServer 启动服务端处理器
func (s *Server) handleStartServer(c *gin.Context) {
	// 从请求体获取端口参数
	var req struct {
		Port int `json:"port" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, errorResponse("无效的请求参数: "+err.Error()))
		return
	}

	s.logger.WithField("port", req.Port).Info("通过API启动iperf3服务端")

	if err := s.serverManager.StartServer(req.Port); err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse("启动服务端失败: "+err.Error()))
		return
	}

	c.JSON(http.StatusOK, messageResponse(fmt.Sprintf("服务端已在端口 %d 启动", req.Port)))
}

// handleStopServer 停止服务端处理器
func (s *Server) handleStopServer(c *gin.Context) {
	// 从请求体获取端口参数
	var req struct {
		Port int `json:"port" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, errorResponse("无效的请求参数: "+err.Error()))
		return
	}

	s.logger.WithField("port", req.Port).Info("通过API停止iperf3服务端")

	if err := s.serverManager.StopServer(req.Port); err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse("停止服务端失败: "+err.Error()))
		return
	}

	c.JSON(http.StatusOK, messageResponse(fmt.Sprintf("服务端已在端口 %d 停止", req.Port)))
}

// handleGetServerInstanceStatus 获取指定端口服务端状态处理器
func (s *Server) handleGetServerInstanceStatus(c *gin.Context) {
	portStr := c.Param("port")
	port, err := strconv.Atoi(portStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, errorResponse("无效的端口参数"))
		return
	}

	status := s.serverManager.GetServerStatus(port)
	if status == nil {
		c.JSON(http.StatusNotFound, errorResponse("未找到指定端口的服务端"))
		return
	}

	c.JSON(http.StatusOK, successResponse(status))
}

// handleManualTest 手动触发测速处理器
func (s *Server) handleManualTest(c *gin.Context) {
	// 从请求体获取测试参数
	var req struct {
		TargetHost string `json:"target_host" binding:"required"`
		Port       int    `json:"port"`
		Duration   int    `json:"duration"`
		Protocol   string `json:"protocol"` // "tcp" or "udp"
		Bandwidth  string `json:"bandwidth"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, errorResponse("无效的请求参数: "+err.Error()))
		return
	}

	// 设置默认值
	if req.Port == 0 {
		req.Port = 55201
	}
	if req.Duration == 0 {
		req.Duration = 30
	}
	if req.Protocol == "" {
		req.Protocol = "tcp"
	}

	s.logger.WithFields(map[string]interface{}{
		"target_host": req.TargetHost,
		"port":        req.Port,
		"duration":    req.Duration,
		"protocol":    req.Protocol,
		"bandwidth":   req.Bandwidth,
	}).Info("通过API手动触发测速")

	// 构建测试配置
	testConfig := &client.TestConfig{
		Type:     req.Protocol,
		Duration: time.Duration(req.Duration) * time.Second,
	}

	// 根据协议类型设置特定配置
	if req.Protocol == "tcp" {
		testConfig.TCP = &client.TCPTestConfig{
			ParallelStreams: 4,
			WindowSize:      "512K",
			MSS:             1460,
		}
	} else if req.Protocol == "udp" {
		bandwidth := req.Bandwidth
		if bandwidth == "" {
			bandwidth = "500M"
		}
		testConfig.UDP = &client.UDPTestConfig{
			Bandwidth:  bandwidth,
			PacketSize: 1472,
		}
	}

	// 触发测试 - 使用调度器触发测试
	ctx := c.Request.Context()
	if err := s.scheduler.TriggerTest(ctx); err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse("触发测速失败: "+err.Error()))
		return
	}

	c.JSON(http.StatusOK, messageResponse("手动测速已触发"))
}
