# iperf3-controller 服务端配置
# 服务器IP: **************
# 动态启动iperf3服务端，测速时启动，测速结束后关闭

# 服务器配置
server:
  host: "0.0.0.0"
  port: 55201
  mode: "server"
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "60s"

# iperf3服务端配置
iperf3_server:
  binary_path: "/usr/bin/iperf3"
  port: 55201
  dynamic_mode: true
  auto_shutdown_timeout: "60s"
  max_connections: 10

# 客户端管理配置
client_management:
  prepare_timeout: "10s"
  test_timeout: "60s"
  stop_timeout: "5s"
  retry_attempts: 3
  retry_delay: "2s"

# 数据库配置
database:
  type: "sqlite"
  path: "/var/lib/iperf3-controller/server-193-123-80-169.db"
  max_connections: 10
  connection_timeout: "5s"

# 日志配置
logging:
  level: "info"
  file: "/var/log/iperf3-controller/server-193-123-80-169.log"
  max_size: "50MB"
  max_backups: 3
  max_age: 7
  compress: true

# Web界面配置
web:
  enabled: false
  static_dir: "/opt/iperf3-controller/web"
  enable_cors: true
  enable_auth: false

# iperf3配置
iperf3:
  binary_path: "/usr/bin/iperf3"
  default_duration: 30
  tcp:
    parallel_streams: 4
    window_size: "512K"
    mss: 1460
  udp:
    bandwidth: "500M"
    packet_size: 1472

# 监控配置
monitoring:
  enable_performance_monitoring: true
  collection_interval: "1m"
  enable_health_check: true
  health_check_interval: "30s"

# 安全配置
security:
  enable_https: false
  allowed_ips:
    - "***********/16"
    - "10.0.0.0/8"
    - "**********/12"
    - "0.0.0.0/0"

# 高级配置
advanced:
  debug_mode: false
  enable_metrics: true
  metrics_port: 9090

# 服务器特定配置
server_info:
  id: "server-193-123-80-169"
  name: "外网测试服务器2"
  description: "用户真实外网测试服务器2 - **************"
  location: "外网节点2"
  tags:
    - "production"
    - "external"
    - "iperf3-server"
