# iperf3-controller 服务端配置
# 服务器IP: **************
# 动态启动iperf3服务端，测速时启动，测速结束后关闭

# 服务器配置
server:
  # 监听地址
  host: "0.0.0.0"
  # 监听端口 (iperf3-controller管理端口)
  port: 55201
  # 运行模式: server (服务端模式)
  mode: "server"
  # 读取超时
  read_timeout: "30s"
  # 写入超时
  write_timeout: "30s"
  # 空闲超时
  idle_timeout: "60s"

# iperf3服务端配置
iperf3_server:
  # iperf3可执行文件路径
  binary_path: "/usr/bin/iperf3"
  # iperf3服务端口 (与管理端口相同)
  port: 55201
  # 动态启动模式
  dynamic_mode: true
  # 测试超时后自动关闭
  auto_shutdown_timeout: "60s"
  # 最大并发连接数
  max_connections: 10

# 客户端管理配置
client_management:
  # 客户端准备超时时间
  prepare_timeout: "10s"
  # 测试执行超时时间
  test_timeout: "60s"
  # 客户端停止超时时间
  stop_timeout: "5s"
  # 重试次数
  retry_attempts: 3
  # 重试延迟
  retry_delay: "2s"

# 数据库配置
database:
  # 数据库类型
  type: "sqlite"
  # 数据库文件路径
  path: "/var/lib/iperf3-controller/server-193-123-92-217.db"
  # 连接池大小
  max_connections: 10
  # 连接超时
  connection_timeout: "5s"

# 日志配置
logging:
  # 日志级别
  level: "info"
  # 日志文件路径
  file: "/var/log/iperf3-controller/server-193-123-92-217.log"
  # 单个日志文件最大大小
  max_size: "50MB"
  # 保留的日志文件数量
  max_backups: 3
  # 日志文件保留天数
  max_age: 7
  # 是否压缩旧日志文件
  compress: true

# Web界面配置 (可选，用于服务器状态监控)
web:
  # 启用Web界面
  enabled: false
  # 静态文件目录
  static_dir: "/opt/iperf3-controller/web"
  # 启用CORS
  enable_cors: true
  # 启用API认证
  enable_auth: false

# iperf3配置
iperf3:
  # iperf3可执行文件路径
  binary_path: "/usr/bin/iperf3"
  # 默认测试持续时间 (秒)
  default_duration: 30
  # TCP测试配置
  tcp:
    # 并行流数量
    parallel_streams: 4
    # TCP窗口大小
    window_size: "512K"
    # 最大段大小
    mss: 1460
  # UDP测试配置
  udp:
    # 目标带宽
    bandwidth: "500M"
    # UDP数据包大小
    packet_size: 1472

# 监控配置
monitoring:
  # 启用性能监控
  enable_performance_monitoring: true
  # 监控数据收集间隔
  collection_interval: "1m"
  # 启用健康检查
  enable_health_check: true
  # 健康检查间隔
  health_check_interval: "30s"

# 安全配置
security:
  # 启用HTTPS
  enable_https: false
  # 允许的IP地址范围 (允许OpenWRT客户端连接)
  allowed_ips:
    - "***********/16"
    - "10.0.0.0/8"
    - "**********/12"
    - "0.0.0.0/0"  # 允许所有IP (生产环境建议限制)

# 高级配置
advanced:
  # 启用调试模式
  debug_mode: false
  # 启用指标收集
  enable_metrics: true
  # 指标端口
  metrics_port: 9090

# 服务器特定配置
server_info:
  # 服务器标识
  id: "server-193-123-92-217"
  # 服务器名称
  name: "外网测试服务器1"
  # 服务器描述
  description: "用户真实外网测试服务器1 - **************"
  # 服务器位置
  location: "外网节点1"
  # 服务器标签
  tags:
    - "production"
    - "external"
    - "iperf3-server"
