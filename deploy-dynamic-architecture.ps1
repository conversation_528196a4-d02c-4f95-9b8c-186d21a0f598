# iperf3-controller 动态架构部署脚本
# OpenWRT客户端 + 6台动态服务端

param(
    [string]$Action = "deploy",
    [switch]$BuildFirst = $false
)

Write-Host "iperf3-controller 动态架构部署脚本" -ForegroundColor Blue
Write-Host "====================================" -ForegroundColor Blue

# 服务器信息
$Servers = @{
    "**************" = @{
        Config = "server-193-123-92-217.yaml"
        Name = "外网测试服务器1"
    }
    "**************" = @{
        Config = "server-193-123-80-169.yaml"
        Name = "外网测试服务器2"
    }
    "**************" = @{
        Config = "server-193-122-52-231.yaml"
        Name = "外网测试服务器3"
    }
    "************" = @{
        Config = "server-138-2-158-93.yaml"
        Name = "外网测试服务器4"
    }
    "*************" = @{
        Config = "server-152-67-209-26.yaml"
        Name = "外网测试服务器5"
    }
    "*************" = @{
        Config = "server-138-2-114-254.yaml"
        Name = "外网测试服务器6"
    }
}

$OpenWRTClients = @{
    "***********" = @{
        Config = "openwrt1-client.yaml"
        Port = 6060
        Name = "OpenWRT客户端1"
        Schedule = "奇数小时"
    }
    "***********3" = @{
        Config = "openwrt2-client.yaml"
        Port = 6066
        Name = "OpenWRT客户端2"
        Schedule = "偶数小时"
    }
}

# 构建程序
if ($BuildFirst) {
    Write-Host "构建程序..." -ForegroundColor Yellow
    .\build-win.ps1 -Target all
    if ($LASTEXITCODE -ne 0) {
        Write-Host "构建失败" -ForegroundColor Red
        exit 1
    }
}

# 部署函数
function Deploy-Server {
    param($ServerIP, $ServerInfo)
    
    Write-Host "部署 $($ServerInfo.Name) ($ServerIP:55201)..." -ForegroundColor Cyan
    
    Write-Host "请在服务器 $ServerIP 上执行以下命令:" -ForegroundColor Yellow
    Write-Host "1. 创建目录:" -ForegroundColor White
    Write-Host "   mkdir -p /opt/iperf3-controller" -ForegroundColor Gray
    Write-Host "   mkdir -p /etc/iperf3-controller" -ForegroundColor Gray
    Write-Host "   mkdir -p /var/log/iperf3-controller" -ForegroundColor Gray
    Write-Host "   mkdir -p /var/lib/iperf3-controller" -ForegroundColor Gray
    
    Write-Host "2. 安装依赖:" -ForegroundColor White
    Write-Host "   # Ubuntu/Debian:" -ForegroundColor Gray
    Write-Host "   apt-get update && apt-get install -y iperf3" -ForegroundColor Gray
    Write-Host "   # CentOS/RHEL:" -ForegroundColor Gray
    Write-Host "   yum install -y iperf3" -ForegroundColor Gray
    
    Write-Host "3. 上传文件:" -ForegroundColor White
    Write-Host "   scp build/iperf3-controller-linux-amd64 root@$($ServerIP):/opt/iperf3-controller/" -ForegroundColor Gray
    Write-Host "   scp $($ServerInfo.Config) root@$($ServerIP):/etc/iperf3-controller/config.yaml" -ForegroundColor Gray
    
    Write-Host "4. 设置权限:" -ForegroundColor White
    Write-Host "   chmod +x /opt/iperf3-controller/iperf3-controller-linux-amd64" -ForegroundColor Gray
    
    Write-Host "5. 创建系统服务:" -ForegroundColor White
    Write-Host "   # 创建systemd服务文件" -ForegroundColor Gray
    Write-Host "   cat > /etc/systemd/system/iperf3-controller.service << 'EOF'" -ForegroundColor Gray
    Write-Host "[Unit]" -ForegroundColor Gray
    Write-Host "Description=iperf3 Controller Server" -ForegroundColor Gray
    Write-Host "After=network.target" -ForegroundColor Gray
    Write-Host "" -ForegroundColor Gray
    Write-Host "[Service]" -ForegroundColor Gray
    Write-Host "Type=simple" -ForegroundColor Gray
    Write-Host "ExecStart=/opt/iperf3-controller/iperf3-controller-linux-amd64 server -c /etc/iperf3-controller/config.yaml" -ForegroundColor Gray
    Write-Host "Restart=always" -ForegroundColor Gray
    Write-Host "RestartSec=5" -ForegroundColor Gray
    Write-Host "" -ForegroundColor Gray
    Write-Host "[Install]" -ForegroundColor Gray
    Write-Host "WantedBy=multi-user.target" -ForegroundColor Gray
    Write-Host "EOF" -ForegroundColor Gray
    
    Write-Host "6. 启动服务:" -ForegroundColor White
    Write-Host "   systemctl daemon-reload" -ForegroundColor Gray
    Write-Host "   systemctl enable iperf3-controller" -ForegroundColor Gray
    Write-Host "   systemctl start iperf3-controller" -ForegroundColor Gray
    
    Write-Host "7. 检查状态:" -ForegroundColor White
    Write-Host "   systemctl status iperf3-controller" -ForegroundColor Gray
    Write-Host "   netstat -tlnp | grep 55201" -ForegroundColor Gray
    Write-Host ""
}

function Deploy-OpenWRT {
    param($ClientIP, $ClientInfo)
    
    Write-Host "部署 $($ClientInfo.Name) ($ClientIP:$($ClientInfo.Port))..." -ForegroundColor Cyan
    
    Write-Host "请在OpenWRT设备 $ClientIP 上执行以下命令:" -ForegroundColor Yellow
    Write-Host "1. 创建目录:" -ForegroundColor White
    Write-Host "   mkdir -p /opt/iperf3-controller" -ForegroundColor Gray
    Write-Host "   mkdir -p /etc/iperf3-controller" -ForegroundColor Gray
    Write-Host "   mkdir -p /var/log/iperf3-controller" -ForegroundColor Gray
    Write-Host "   mkdir -p /var/lib/iperf3-controller" -ForegroundColor Gray
    
    Write-Host "2. 安装依赖:" -ForegroundColor White
    Write-Host "   opkg update" -ForegroundColor Gray
    Write-Host "   opkg install iperf3" -ForegroundColor Gray
    
    Write-Host "3. 上传文件:" -ForegroundColor White
    Write-Host "   scp build/iperf3-controller-openwrt-mips root@$($ClientIP):/opt/iperf3-controller/" -ForegroundColor Gray
    Write-Host "   scp $($ClientInfo.Config) root@$($ClientIP):/etc/iperf3-controller/config.yaml" -ForegroundColor Gray
    Write-Host "   scp -r build/static root@$($ClientIP):/opt/iperf3-controller/web" -ForegroundColor Gray
    
    Write-Host "4. 设置权限:" -ForegroundColor White
    Write-Host "   chmod +x /opt/iperf3-controller/iperf3-controller-openwrt-mips" -ForegroundColor Gray
    
    Write-Host "5. 创建启动脚本:" -ForegroundColor White
    Write-Host "   cat > /etc/init.d/iperf3-controller << 'EOF'" -ForegroundColor Gray
    Write-Host "#!/bin/sh /etc/rc.common" -ForegroundColor Gray
    Write-Host "START=99" -ForegroundColor Gray
    Write-Host "STOP=10" -ForegroundColor Gray
    Write-Host "USE_PROCD=1" -ForegroundColor Gray
    Write-Host "" -ForegroundColor Gray
    Write-Host "start_service() {" -ForegroundColor Gray
    Write-Host "    procd_open_instance" -ForegroundColor Gray
    Write-Host "    procd_set_param command /opt/iperf3-controller/iperf3-controller-openwrt-mips client -c /etc/iperf3-controller/config.yaml" -ForegroundColor Gray
    Write-Host "    procd_set_param respawn" -ForegroundColor Gray
    Write-Host "    procd_close_instance" -ForegroundColor Gray
    Write-Host "}" -ForegroundColor Gray
    Write-Host "EOF" -ForegroundColor Gray
    
    Write-Host "6. 启动服务:" -ForegroundColor White
    Write-Host "   chmod +x /etc/init.d/iperf3-controller" -ForegroundColor Gray
    Write-Host "   /etc/init.d/iperf3-controller enable" -ForegroundColor Gray
    Write-Host "   /etc/init.d/iperf3-controller start" -ForegroundColor Gray
    
    Write-Host "7. 访问Web界面:" -ForegroundColor White
    Write-Host "   http://$ClientIP:$($ClientInfo.Port)" -ForegroundColor Green
    Write-Host "   调度模式: $($ClientInfo.Schedule)" -ForegroundColor Green
    Write-Host ""
}

# 执行部署
if ($Action -eq "deploy") {
    Write-Host "开始部署动态架构..." -ForegroundColor Green
    Write-Host ""
    
    # 部署服务端
    Write-Host "=== 部署6台动态服务端 ===" -ForegroundColor Magenta
    foreach ($ip in $Servers.Keys) {
        Deploy-Server $ip $Servers[$ip]
    }
    
    # 部署OpenWRT客户端
    Write-Host "=== 部署OpenWRT客户端 ===" -ForegroundColor Magenta
    foreach ($ip in $OpenWRTClients.Keys) {
        Deploy-OpenWRT $ip $OpenWRTClients[$ip]
    }
    
    # 架构说明
    Write-Host "=== 动态架构说明 ===" -ForegroundColor Magenta
    Write-Host "🔄 动态iperf3服务端:" -ForegroundColor Yellow
    Write-Host "  - 6台服务器运行iperf3-controller程序" -ForegroundColor White
    Write-Host "  - 统一监听55201端口" -ForegroundColor White
    Write-Host "  - 测速时动态启动iperf3服务端" -ForegroundColor White
    Write-Host "  - 测速结束后自动关闭iperf3进程" -ForegroundColor White
    Write-Host ""
    Write-Host "📱 OpenWRT客户端:" -ForegroundColor Yellow
    Write-Host "  - OpenWRT1: 奇数小时测试 (http://***********:6060)" -ForegroundColor White
    Write-Host "  - OpenWRT2: 偶数小时测试 (http://***********3:6066)" -ForegroundColor White
    Write-Host "  - 主动连接6台服务器进行测试" -ForegroundColor White
    Write-Host "  - 双OpenWRT数据同步" -ForegroundColor White
    Write-Host ""
    
} elseif ($Action -eq "status") {
    Write-Host "检查服务状态..." -ForegroundColor Green
    
    # 检查服务端状态
    Write-Host "=== 服务端状态 ===" -ForegroundColor Magenta
    foreach ($ip in $Servers.Keys) {
        Write-Host "检查 $($Servers[$ip].Name) ($ip:55201)..." -ForegroundColor Cyan
        try {
            $response = Invoke-WebRequest -Uri "http://$ip:55201/health" -TimeoutSec 5 -ErrorAction Stop
            Write-Host "  ✅ $($Servers[$ip].Name) 运行正常" -ForegroundColor Green
        } catch {
            Write-Host "  ❌ $($Servers[$ip].Name) 无法访问" -ForegroundColor Red
        }
    }
    
    # 检查OpenWRT客户端状态
    Write-Host "=== OpenWRT客户端状态 ===" -ForegroundColor Magenta
    foreach ($ip in $OpenWRTClients.Keys) {
        $client = $OpenWRTClients[$ip]
        Write-Host "检查 $($client.Name) ($ip:$($client.Port))..." -ForegroundColor Cyan
        try {
            $response = Invoke-WebRequest -Uri "http://$ip:$($client.Port)/health" -TimeoutSec 5 -ErrorAction Stop
            Write-Host "  ✅ $($client.Name) 运行正常" -ForegroundColor Green
        } catch {
            Write-Host "  ❌ $($client.Name) 无法访问" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "部署脚本完成!" -ForegroundColor Green
Write-Host "使用方法:" -ForegroundColor Cyan
Write-Host "  .\deploy-dynamic-architecture.ps1 -Action deploy    # 显示部署指令" -ForegroundColor White
Write-Host "  .\deploy-dynamic-architecture.ps1 -Action status    # 检查服务状态" -ForegroundColor White
Write-Host "  .\deploy-dynamic-architecture.ps1 -BuildFirst       # 先构建再部署" -ForegroundColor White
