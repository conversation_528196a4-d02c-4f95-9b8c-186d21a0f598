# iperf3服务器IP列表配置 - 动态架构

## 📋 用户提供的真实服务器IP

根据用户提供的6台真实外网服务器IP，采用动态iperf3服务端架构：

| 序号 | IP地址 | 端口 | 服务器ID | 配置文件 | 描述 |
|------|--------|------|----------|----------|------|
| 1 | ************** | 55201 | server-193-123-92-217 | server-193-123-92-217.yaml | 动态iperf3服务端1 |
| 2 | ************** | 55201 | server-193-123-80-169 | server-193-123-80-169.yaml | 动态iperf3服务端2 |
| 3 | ************** | 55201 | server-193-122-52-231 | server-193-122-52-231.yaml | 动态iperf3服务端3 |
| 4 | ************ | 55201 | server-138-2-158-93 | server-138-2-158-93.yaml | 动态iperf3服务端4 |
| 5 | ************* | 55201 | server-152-67-209-26 | server-152-67-209-26.yaml | 动态iperf3服务端5 |
| 6 | ************* | 55201 | server-138-2-114-254 | server-138-2-114-254.yaml | 动态iperf3服务端6 |

## 🔧 配置文件更新

### 已更新的配置文件
- ✅ `openwrt1-client.yaml` - OpenWRT1客户端配置
- ✅ `openwrt2-client.yaml` - OpenWRT2客户端配置

### 🔄 动态架构特点
- **统一端口**: 所有服务器使用55201端口
- **动态启动**: 测速时启动iperf3服务端，测速结束后关闭
- **Go程序控制**: 通过iperf3-controller程序管理iperf3进程
- **服务器数量**: 6台真实服务器
- **命名规则**: server-{ip-with-dashes}
- **状态**: 所有服务器默认启用 (enabled: true)

## 🌐 网络架构

### OpenWRT客户端配置
```
OpenWRT1 (***********:6060) ←→ OpenWRT2 (************:6066)
        ↓                              ↓
    奇数小时测试                    偶数小时测试
        ↓                              ↓
    连接6台外网服务器              连接6台外网服务器
```

### 🚀 动态服务器端部署
每台服务器运行iperf3-controller程序（服务端模式）：
```bash
# 在每台服务器上运行
./iperf3-controller-linux-amd64 server -c /etc/iperf3-controller/config.yaml

# 程序会：
# 1. 监听55201端口等待OpenWRT客户端连接
# 2. 收到测试请求时动态启动iperf3服务端
# 3. 测试完成后自动关闭iperf3进程
# 4. 记录测试结果到数据库
```

### 📋 服务器配置文件
- `server-193-123-92-217.yaml` - 服务器1配置
- `server-193-123-80-169.yaml` - 服务器2配置
- `server-193-122-52-231.yaml` - 服务器3配置
- `server-138-2-158-93.yaml` - 服务器4配置
- `server-152-67-209-26.yaml` - 服务器5配置
- `server-138-2-114-254.yaml` - 服务器6配置

## 🔄 双OpenWRT互连配置

### OpenWRT1 → OpenWRT2 同步
```yaml
sync:
  peer_host: "************"  # OpenWRT2的IP
  peer_port: 6066            # OpenWRT2的端口
```

### OpenWRT2 → OpenWRT1 同步  
```yaml
sync:
  peer_host: "***********"   # OpenWRT1的IP
  peer_port: 6060            # OpenWRT1的端口
```

### 同步机制
- **同步间隔**: 30秒
- **心跳间隔**: 10秒
- **连接超时**: 5秒
- **重试机制**: 3次重试，延迟2秒
- **数据压缩**: 启用
- **数据加密**: 禁用（内网环境）

## ✅ 互连验证

### 网络连通性检查
两台OpenWRT设备能够互相连接的前提条件：

1. **网络可达性**
   - *********** 能够访问 ************:6066
   - ************ 能够访问 ***********:6060

2. **防火墙配置**
   - OpenWRT1开放6060端口
   - OpenWRT2开放6066端口

3. **路由配置**
   - 两台设备在同一网段或有正确的路由配置

### 验证命令
```bash
# 在OpenWRT1上测试连接OpenWRT2
telnet ************ 6066

# 在OpenWRT2上测试连接OpenWRT1  
telnet *********** 6060

# 或使用ping测试基本连通性
ping ************  # 从OpenWRT1
ping ***********   # 从OpenWRT2
```

## 🎯 测试调度

### 错峰测试机制
- **OpenWRT1**: 奇数小时测试 (1点、3点、5点、7点...)
- **OpenWRT2**: 偶数小时测试 (2点、4点、6点、8点...)
- **数据同步**: 每30秒同步测试结果

### 抢占式测试
- 批次大小: 3台服务器同时测试
- 批次延迟: 3秒
- 测试超时: 60秒
- 重试机制: 3次重试

## 📊 预期结果

配置完成后，系统将实现：
1. ✅ 两台OpenWRT设备错峰测试6台外网服务器
2. ✅ 实时数据同步和备份
3. ✅ Web界面统一管理和监控
4. ✅ 自动化调度和故障恢复
