package api

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"iperf3-controller/internal/client"
	"iperf3-controller/internal/coordinator"
	"iperf3-controller/internal/database"
	"iperf3-controller/internal/iperf3"
	"iperf3-controller/internal/scheduler"
	"iperf3-controller/internal/sync"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// Server Web API服务器
type Server struct {
	router     *gin.Engine
	httpServer *http.Server
	logger     *logrus.Logger

	// 核心组件
	clientManager   *client.Manager
	testCoordinator coordinator.TestCoordinator
	scheduler       scheduler.Scheduler
	syncManager     sync.SyncManager
	repository      *database.Repository
	serverManager   iperf3.ServerManager

	// 配置
	config *ServerConfig
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Host         string        `json:"host"`
	Port         int           `json:"port"`
	ReadTimeout  time.Duration `json:"read_timeout"`
	WriteTimeout time.Duration `json:"write_timeout"`
	IdleTimeout  time.Duration `json:"idle_timeout"`
	EnableCORS   bool          `json:"enable_cors"`
	EnableAuth   bool          `json:"enable_auth"`
	StaticDir    string        `json:"static_dir"`
}

// NewServer 创建新的API服务器
func NewServer(
	config *ServerConfig,
	clientManager *client.Manager,
	testCoordinator coordinator.TestCoordinator,
	scheduler scheduler.Scheduler,
	syncManager sync.SyncManager,
	repository *database.Repository,
	serverManager iperf3.ServerManager,
	logger *logrus.Logger,
) *Server {
	if logger == nil {
		logger = logrus.New()
	}

	// 设置Gin模式
	if logger.Level == logrus.DebugLevel {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	server := &Server{
		router:          gin.New(),
		logger:          logger,
		clientManager:   clientManager,
		testCoordinator: testCoordinator,
		scheduler:       scheduler,
		syncManager:     syncManager,
		repository:      repository,
		serverManager:   serverManager,
		config:          config,
	}

	// 设置中间件
	server.setupMiddleware()

	// 设置路由
	server.setupRoutes()

	return server
}

// setupMiddleware 设置中间件
func (s *Server) setupMiddleware() {
	// 日志中间件
	s.router.Use(gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		return fmt.Sprintf("%s - [%s] \"%s %s %s %d %s \"%s\" %s\"\n",
			param.ClientIP,
			param.TimeStamp.Format("02/Jan/2006:15:04:05 -0700"),
			param.Method,
			param.Path,
			param.Request.Proto,
			param.StatusCode,
			param.Latency,
			param.Request.UserAgent(),
			param.ErrorMessage,
		)
	}))

	// 恢复中间件
	s.router.Use(gin.Recovery())

	// CORS中间件
	if s.config.EnableCORS {
		s.router.Use(func(c *gin.Context) {
			c.Header("Access-Control-Allow-Origin", "*")
			c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
			c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")

			if c.Request.Method == "OPTIONS" {
				c.AbortWithStatus(204)
				return
			}

			c.Next()
		})
	}

	// 认证中间件（如果启用）
	if s.config.EnableAuth {
		s.router.Use(s.authMiddleware())
	}
}

// authMiddleware 认证中间件
func (s *Server) authMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 简单的API密钥认证
		apiKey := c.GetHeader("X-API-Key")
		if apiKey == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "缺少API密钥",
			})
			c.Abort()
			return
		}

		// 这里可以验证API密钥的有效性
		// 目前简单检查是否为预设值
		if apiKey != "iperf3-controller-api-key" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "无效的API密钥",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// setupRoutes 设置路由
func (s *Server) setupRoutes() {
	// 健康检查
	s.router.GET("/health", s.handleHealth)

	// API v1路由组
	v1 := s.router.Group("/api/v1")
	{
		// 系统状态
		v1.GET("/status", s.handleSystemStatus)

		// 客户端管理
		clients := v1.Group("/clients")
		{
			clients.GET("", s.handleGetClients)
			clients.POST("", s.handleAddClient)
			clients.GET("/:id", s.handleGetClient)
			clients.PUT("/:id", s.handleUpdateClient)
			clients.DELETE("/:id", s.handleDeleteClient)
			clients.POST("/:id/test", s.handleTestClient)
		}

		// 测试管理
		tests := v1.Group("/tests")
		{
			tests.GET("", s.handleGetTests)
			tests.POST("/trigger", s.handleTriggerTest)
			tests.POST("/manual", s.handleManualTest)
			tests.GET("/current", s.handleGetCurrentTest)
			tests.DELETE("/current", s.handleCancelCurrentTest)
			tests.GET("/history", s.handleGetTestHistory)
		}

		// 服务端管理
		servers := v1.Group("/servers")
		{
			servers.GET("/status", s.handleServerStatus)
			servers.POST("/start", s.handleStartServer)
			servers.POST("/stop", s.handleStopServer)
			servers.GET("/:port/status", s.handleGetServerInstanceStatus)
		}

		// 调度管理
		schedule := v1.Group("/schedule")
		{
			schedule.GET("/status", s.handleGetScheduleStatus)
			schedule.POST("/start", s.handleStartSchedule)
			schedule.POST("/stop", s.handleStopSchedule)
			schedule.POST("/trigger", s.handleTriggerSchedule)
			schedule.GET("/next", s.handleGetNextSchedule)
		}

		// 同步管理
		syncGroup := v1.Group("/sync")
		{
			syncGroup.GET("/status", s.handleGetSyncStatus)
			syncGroup.POST("/start", s.handleStartSync)
			syncGroup.POST("/stop", s.handleStopSync)
			syncGroup.POST("/trigger", s.handleTriggerSync)
			syncGroup.GET("/peer", s.handleGetPeerStatus)
		}

		// 统计数据
		stats := v1.Group("/stats")
		{
			stats.GET("/overview", s.handleGetStatsOverview)
			stats.GET("/performance", s.handleGetPerformanceStats)
			stats.GET("/clients", s.handleGetClientStats)
			stats.GET("/tests", s.handleGetTestStats)
		}
	}

	// 静态文件服务
	if s.config.StaticDir != "" {
		s.router.Static("/static", s.config.StaticDir)
		s.router.StaticFile("/", s.config.StaticDir+"/index.html")
	}
}

// Start 启动服务器
func (s *Server) Start() error {
	addr := fmt.Sprintf("%s:%d", s.config.Host, s.config.Port)

	s.httpServer = &http.Server{
		Addr:         addr,
		Handler:      s.router,
		ReadTimeout:  s.config.ReadTimeout,
		WriteTimeout: s.config.WriteTimeout,
		IdleTimeout:  s.config.IdleTimeout,
	}

	s.logger.WithFields(logrus.Fields{
		"host": s.config.Host,
		"port": s.config.Port,
	}).Info("启动Web API服务器")

	if err := s.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		return fmt.Errorf("启动HTTP服务器失败: %w", err)
	}

	return nil
}

// Stop 停止服务器
func (s *Server) Stop(ctx context.Context) error {
	s.logger.Info("停止Web API服务器")

	if s.httpServer != nil {
		return s.httpServer.Shutdown(ctx)
	}

	return nil
}

// GetRouter 获取Gin路由器（用于测试）
func (s *Server) GetRouter() *gin.Engine {
	return s.router
}

// 通用响应结构
type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

// 分页响应结构
type PaginatedResponse struct {
	APIResponse
	Page       int `json:"page"`
	PageSize   int `json:"page_size"`
	Total      int `json:"total"`
	TotalPages int `json:"total_pages"`
}

// successResponse 成功响应
func successResponse(data interface{}) APIResponse {
	return APIResponse{
		Success: true,
		Data:    data,
	}
}

// errorResponse 错误响应
func errorResponse(message string) APIResponse {
	return APIResponse{
		Success: false,
		Error:   message,
	}
}

// messageResponse 消息响应
func messageResponse(message string) APIResponse {
	return APIResponse{
		Success: true,
		Message: message,
	}
}
