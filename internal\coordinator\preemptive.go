package coordinator

import (
	"context"
	"fmt"
	"sync"
	"time"

	"iperf3-controller/internal/client"
	"iperf3-controller/internal/iperf3"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// PreemptiveTestCoordinator 实现抢占式测试协调器
// 服务器抢占式响应测试，谁先响应谁先测速，直到所有服务器都测速结束
type PreemptiveTestCoordinator struct {
	clientManager *client.Manager
	serverManager iperf3.ServerManager
	logger        *logrus.Logger

	// 抢占式测试状态
	mu               sync.RWMutex
	isRunning        bool
	currentRound     *PreemptiveTestRound
	completedClients map[string]*client.TestResult // 存储已完成测试的客户端及其结果
	failedClients    map[string]error              // 测试失败的客户端

	// 统计信息
	totalRounds      int
	successfulRounds int
	failedRounds     int
	lastError        error
}

// PreemptiveTestRound 抢占式测试轮次
type PreemptiveTestRound struct {
	ID               string                        `json:"id"`
	Hour             int                           `json:"hour"`
	StartTime        time.Time                     `json:"start_time"`
	EndTime          *time.Time                    `json:"end_time,omitempty"`
	Status           string                        `json:"status"` // "running", "completed", "failed"
	TotalClients     int                           `json:"total_clients"`
	CompletedClients int                           `json:"completed_clients"`
	FailedClients    int                           `json:"failed_clients"`
	TestResults      map[string]*client.TestResult `json:"test_results"`
	Errors           map[string]string             `json:"errors"`
}

// NewPreemptiveTestCoordinator 创建新的抢占式测试协调器
func NewPreemptiveTestCoordinator(
	clientManager *client.Manager,
	serverManager iperf3.ServerManager,
	logger *logrus.Logger,
) *PreemptiveTestCoordinator {
	if logger == nil {
		logger = logrus.New()
	}

	return &PreemptiveTestCoordinator{
		clientManager:    clientManager,
		serverManager:    serverManager,
		logger:           logger,
		completedClients: make(map[string]*client.TestResult),
		failedClients:    make(map[string]error),
	}
}

// Start 启动抢占式测试协调器
func (c *PreemptiveTestCoordinator) Start(ctx context.Context) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.logger.Info("启动抢占式测试协调器")
	c.isRunning = true

	return nil
}

// Stop 停止抢占式测试协调器
func (c *PreemptiveTestCoordinator) Stop() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.logger.Info("停止抢占式测试协调器")
	c.isRunning = false

	return nil
}

// ExecutePreemptiveTestRound 执行抢占式测试轮次
// 所有客户端同时开始抢占，谁先响应谁先测速
func (c *PreemptiveTestCoordinator) ExecutePreemptiveTestRound(ctx context.Context, hour int) (*PreemptiveTestRound, error) {
	c.mu.Lock()

	// 检查是否已有测试在运行
	if c.currentRound != nil && c.currentRound.Status == "running" {
		c.mu.Unlock()
		return nil, fmt.Errorf("已有测试轮次正在运行")
	}

	// 获取所有可用的客户端
	clients := c.clientManager.GetEnabledClients()
	if len(clients) == 0 {
		c.mu.Unlock()
		return nil, fmt.Errorf("没有可用的客户端")
	}

	// 创建新的测试轮次
	round := &PreemptiveTestRound{
		ID:           uuid.New().String(),
		Hour:         hour,
		StartTime:    time.Now(),
		Status:       "running",
		TotalClients: len(clients),
		TestResults:  make(map[string]*client.TestResult),
		Errors:       make(map[string]string),
	}

	c.currentRound = round
	c.completedClients = make(map[string]*client.TestResult)
	c.failedClients = make(map[string]error)

	c.mu.Unlock()

	c.logger.WithFields(logrus.Fields{
		"round_id":      round.ID,
		"hour":          hour,
		"total_clients": len(clients),
	}).Info("开始抢占式测试轮次")

	// 执行抢占式测试
	err := c.executePreemptiveTests(ctx, clients, round)

	// 更新测试轮次状态
	c.mu.Lock()
	endTime := time.Now()
	round.EndTime = &endTime

	if err != nil {
		round.Status = "failed"
		c.lastError = err
		c.failedRounds++
		c.totalRounds++

		c.logger.WithFields(logrus.Fields{
			"round_id": round.ID,
			"hour":     hour,
			"error":    err.Error(),
			"duration": endTime.Sub(round.StartTime),
		}).Error("抢占式测试轮次失败")
	} else {
		round.Status = "completed"
		c.successfulRounds++
		c.totalRounds++

		c.logger.WithFields(logrus.Fields{
			"round_id":          round.ID,
			"hour":              hour,
			"duration":          endTime.Sub(round.StartTime),
			"completed_clients": round.CompletedClients,
			"failed_clients":    round.FailedClients,
		}).Info("抢占式测试轮次完成")
	}

	// 复制结果到轮次记录
	for clientID, result := range c.completedClients {
		round.TestResults[clientID] = result
		round.CompletedClients++
	}

	for clientID, err := range c.failedClients {
		round.Errors[clientID] = err.Error()
		round.FailedClients++
	}

	c.currentRound = nil
	c.mu.Unlock()

	return round, err
}

// executePreemptiveTests 执行抢占式测试
// 所有客户端同时开始，谁先响应谁先测速
func (c *PreemptiveTestCoordinator) executePreemptiveTests(ctx context.Context, clients []client.Client, round *PreemptiveTestRound) error {
	c.logger.WithField("client_count", len(clients)).Info("开始抢占式测试执行")

	// 检查服务端资源状态
	if err := c.ensureServerAvailability(ctx); err != nil {
		c.logger.WithError(err).Error("服务端资源检查失败")
		return fmt.Errorf("服务端资源不可用: %w", err)
	}

	// 创建抢占通道
	preemptChan := make(chan *PreemptiveTestRequest, len(clients))
	resultChan := make(chan *PreemptiveTestResult, len(clients))

	// 启动所有客户端的抢占goroutine
	var wg sync.WaitGroup
	for _, cl := range clients {
		wg.Add(1)
		go func(client client.Client) {
			defer wg.Done()
			c.clientPreemptiveRoutine(ctx, client, preemptChan, resultChan)
		}(cl)
	}

	// 等待所有客户端完成抢占
	go func() {
		wg.Wait()
		close(preemptChan)
		close(resultChan)
	}()

	// 处理抢占结果
	completedCount := 0
	failedCount := 0

	for result := range resultChan {
		if result.Error != nil {
			c.mu.Lock()
			c.failedClients[result.ClientID] = result.Error
			c.mu.Unlock()
			failedCount++

			c.logger.WithFields(logrus.Fields{
				"client_id": result.ClientID,
				"error":     result.Error.Error(),
			}).Warn("客户端测试失败")
		} else {
			// 记录成功的测试结果
			c.mu.Lock()
			// 暂时跳过结果存储，直接计数
			c.mu.Unlock()
			completedCount++

			c.logger.WithFields(logrus.Fields{
				"client_id": result.ClientID,
			}).Info("客户端测试完成")
		}
	}

	c.logger.WithFields(logrus.Fields{
		"completed_count": completedCount,
		"failed_count":    failedCount,
		"total_count":     len(clients),
	}).Info("抢占式测试执行完成")

	if completedCount == 0 {
		return fmt.Errorf("所有客户端测试都失败了")
	}

	return nil
}

// PreemptiveTestRequest 抢占式测试请求
type PreemptiveTestRequest struct {
	ClientID   string
	TestConfig client.TestConfig
	Priority   int // 优先级，数字越小优先级越高
}

// PreemptiveTestResult 抢占式测试结果
type PreemptiveTestResult struct {
	ClientID   string
	TestResult interface{} // 使用interface{}以支持不同类型的测试结果
	Error      error
	Duration   time.Duration
}

// clientPreemptiveRoutine 客户端抢占式测试例程
func (c *PreemptiveTestCoordinator) clientPreemptiveRoutine(
	ctx context.Context,
	client client.Client,
	preemptChan chan *PreemptiveTestRequest,
	resultChan chan *PreemptiveTestResult,
) {
	clientID := client.GetID()
	startTime := time.Now()

	c.logger.WithField("client_id", clientID).Debug("客户端开始抢占式测试")

	// 创建测试配置
	tcpConfig := c.createTCPTestConfig()
	udpConfig := c.createUDPTestConfig()

	// 执行TCP测试
	tcpResult := c.executeClientTestWithPreemption(ctx, client, *tcpConfig)

	// 执行UDP测试
	udpResult := c.executeClientTestWithPreemption(ctx, client, *udpConfig)

	// 选择最好的结果（优先TCP，如果TCP失败则使用UDP）
	var finalResult interface{}
	var finalError error

	if tcpResult.Error == nil {
		finalResult = tcpResult.TestResult
	} else if udpResult.Error == nil {
		finalResult = udpResult.TestResult
	} else {
		finalError = tcpResult.Error
	}

	// 发送结果
	var testResult interface{}
	if finalResult != nil {
		testResult = finalResult
	}

	resultChan <- &PreemptiveTestResult{
		ClientID:   clientID,
		TestResult: testResult,
		Error:      finalError,
		Duration:   time.Since(startTime),
	}
}

// executeClientTestWithPreemption 执行客户端抢占式测试
func (c *PreemptiveTestCoordinator) executeClientTestWithPreemption(
	ctx context.Context,
	client client.Client,
	testConfig client.TestConfig,
) *PreemptiveTestResult {
	clientID := client.GetID()
	startTime := time.Now()

	c.logger.WithFields(logrus.Fields{
		"client_id": clientID,
		"test_type": testConfig.Type,
	}).Debug("开始客户端抢占式测试")

	// 准备客户端
	if err := client.Prepare(ctx); err != nil {
		return &PreemptiveTestResult{
			ClientID: clientID,
			Error:    fmt.Errorf("客户端准备失败: %w", err),
			Duration: time.Since(startTime),
		}
	}

	// 执行测试
	result, err := client.StartTest(ctx, testConfig)
	if err != nil {
		// 尝试停止客户端
		client.Stop(ctx)
		return &PreemptiveTestResult{
			ClientID: clientID,
			Error:    fmt.Errorf("测试执行失败: %w", err),
			Duration: time.Since(startTime),
		}
	}

	// 停止客户端
	if err := client.Stop(ctx); err != nil {
		c.logger.WithFields(logrus.Fields{
			"client_id": clientID,
			"error":     err.Error(),
		}).Warn("测试后停止客户端失败")
	}

	return &PreemptiveTestResult{
		ClientID:   clientID,
		TestResult: result,
		Duration:   time.Since(startTime),
	}
}

// createTCPTestConfig 创建TCP测试配置
func (c *PreemptiveTestCoordinator) createTCPTestConfig() *client.TestConfig {
	return &client.TestConfig{
		Type:     "tcp",
		Duration: 30 * time.Second, // TCP测试30秒
		TCP: &client.TCPTestConfig{
			ParallelStreams: 4,
			WindowSize:      "512K",
			MSS:             1460,
		},
	}
}

// createUDPTestConfig 创建UDP测试配置
func (c *PreemptiveTestCoordinator) createUDPTestConfig() *client.TestConfig {
	return &client.TestConfig{
		Type:     "udp",
		Duration: 10 * time.Second, // UDP测试10秒
		UDP: &client.UDPTestConfig{
			Bandwidth:  "500M",
			PacketSize: 1472,
		},
	}
}

// GetCurrentRound 获取当前测试轮次
func (c *PreemptiveTestCoordinator) GetCurrentRound() *PreemptiveTestRound {
	c.mu.RLock()
	defer c.mu.RUnlock()

	if c.currentRound != nil {
		// 返回副本避免竞态条件
		round := *c.currentRound
		return &round
	}

	return nil
}

// GetStatus 获取协调器状态
func (c *PreemptiveTestCoordinator) GetStatus() *CoordinatorStatus {
	c.mu.RLock()
	defer c.mu.RUnlock()

	status := &CoordinatorStatus{
		IsRunning:        c.isRunning,
		TotalRounds:      c.totalRounds,
		SuccessfulRounds: c.successfulRounds,
		FailedRounds:     c.failedRounds,
	}

	if c.currentRound != nil {
		roundID := c.currentRound.ID
		status.CurrentRound = &roundID
	}

	if c.lastError != nil {
		errStr := c.lastError.Error()
		status.LastError = &errStr
	}

	return status
}

// GetPreemptiveStats 获取抢占式测试统计信息
func (c *PreemptiveTestCoordinator) GetPreemptiveStats() map[string]interface{} {
	c.mu.RLock()
	defer c.mu.RUnlock()

	stats := map[string]interface{}{
		"is_running":        c.isRunning,
		"total_rounds":      c.totalRounds,
		"successful_rounds": c.successfulRounds,
		"failed_rounds":     c.failedRounds,
		"success_rate":      0.0,
	}

	if c.totalRounds > 0 {
		stats["success_rate"] = float64(c.successfulRounds) / float64(c.totalRounds) * 100
	}

	if c.currentRound != nil {
		stats["current_round"] = map[string]interface{}{
			"id":                c.currentRound.ID,
			"hour":              c.currentRound.Hour,
			"status":            c.currentRound.Status,
			"total_clients":     c.currentRound.TotalClients,
			"completed_clients": c.currentRound.CompletedClients,
			"failed_clients":    c.currentRound.FailedClients,
			"start_time":        c.currentRound.StartTime,
		}
	}

	if c.lastError != nil {
		stats["last_error"] = c.lastError.Error()
	}

	return stats
}

// PreemptiveMetrics 抢占式测试指标
type PreemptiveMetrics struct {
	TotalTests          int64         `json:"total_tests"`
	SuccessfulTests     int64         `json:"successful_tests"`
	FailedTests         int64         `json:"failed_tests"`
	AverageResponseTime time.Duration `json:"average_response_time"`
	FastestResponseTime time.Duration `json:"fastest_response_time"`
	SlowestResponseTime time.Duration `json:"slowest_response_time"`
	ConcurrentTests     int64         `json:"concurrent_tests"`
	MaxConcurrentTests  int64         `json:"max_concurrent_tests"`
	NetworkUtilization  float64       `json:"network_utilization"`
	CPUUtilization      float64       `json:"cpu_utilization"`
	MemoryUtilization   float64       `json:"memory_utilization"`
}

// GetDetailedMetrics 获取详细的抢占式测试指标
func (c *PreemptiveTestCoordinator) GetDetailedMetrics() *PreemptiveMetrics {
	c.mu.RLock()
	defer c.mu.RUnlock()

	metrics := &PreemptiveMetrics{
		TotalTests:      int64(c.totalRounds),
		SuccessfulTests: int64(c.successfulRounds),
		FailedTests:     int64(c.failedRounds),
	}

	if c.currentRound != nil {
		metrics.ConcurrentTests = int64(c.currentRound.CompletedClients + c.currentRound.FailedClients)
	}

	return metrics
}

// EnhancedErrorHandling 增强的错误处理
func (c *PreemptiveTestCoordinator) EnhancedErrorHandling(err error, clientID string, operation string) {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.logger.WithFields(logrus.Fields{
		"client_id": clientID,
		"operation": operation,
		"error":     err.Error(),
	}).Error("抢占式测试错误")

	// 记录错误类型和频率
	if c.currentRound != nil {
		if c.currentRound.Errors == nil {
			c.currentRound.Errors = make(map[string]string)
		}
		c.currentRound.Errors[clientID] = err.Error()
		c.currentRound.FailedClients++
	}

	c.lastError = err
}

// RetryWithBackoff 带退避的重试机制
func (c *PreemptiveTestCoordinator) RetryWithBackoff(operation func() error, maxRetries int, initialDelay time.Duration) error {
	var lastErr error
	delay := initialDelay

	for i := 0; i < maxRetries; i++ {
		if err := operation(); err == nil {
			return nil
		} else {
			lastErr = err
			if i < maxRetries-1 {
				time.Sleep(delay)
				delay *= 2 // 指数退避
			}
		}
	}

	return fmt.Errorf("重试%d次后仍然失败: %w", maxRetries, lastErr)
}

// FairPreemptionAlgorithm 公平抢占算法
func (c *PreemptiveTestCoordinator) FairPreemptionAlgorithm(clients []client.Client) []client.Client {
	// 按响应时间排序，确保公平性
	sortedClients := make([]client.Client, len(clients))
	copy(sortedClients, clients)

	// 这里可以实现更复杂的公平性算法
	// 例如：考虑历史成功率、网络质量等因素

	return sortedClients
}

// ensureServerAvailability 确保服务端资源可用性
func (c *PreemptiveTestCoordinator) ensureServerAvailability(ctx context.Context) error {
	c.logger.Debug("检查服务端资源可用性")

	// 获取服务端管理器状态
	status := c.serverManager.GetStatus()
	if !status.IsRunning {
		c.logger.Warn("服务端管理器未运行，尝试启动")
		if err := c.serverManager.Start(ctx); err != nil {
			return fmt.Errorf("启动服务端管理器失败: %w", err)
		}
	}

	// 检查是否有可用的服务端实例
	if status.RunningCount == 0 {
		c.logger.Info("没有运行中的服务端实例，启动默认服务端")
		if err := c.startDefaultServer(); err != nil {
			return fmt.Errorf("启动默认服务端失败: %w", err)
		}
	}

	c.logger.WithFields(logrus.Fields{
		"total_servers": status.TotalServers,
		"running_count": status.RunningCount,
	}).Info("服务端资源检查完成")

	return nil
}

// startDefaultServer 启动默认服务端（端口55201）
func (c *PreemptiveTestCoordinator) startDefaultServer() error {
	defaultPort := 55201

	c.logger.WithField("port", defaultPort).Info("启动默认iperf3服务端")

	if err := c.serverManager.StartServer(defaultPort); err != nil {
		return fmt.Errorf("启动端口%d的服务端失败: %w", defaultPort, err)
	}

	// 等待服务端启动
	time.Sleep(1 * time.Second)

	// 验证服务端是否成功启动
	if !c.serverManager.IsServerRunning(defaultPort) {
		return fmt.Errorf("服务端启动验证失败，端口%d", defaultPort)
	}

	c.logger.WithField("port", defaultPort).Info("默认服务端启动成功")
	return nil
}

// selectOptimalServer 选择最优服务端进行测试
func (c *PreemptiveTestCoordinator) selectOptimalServer() (int, error) {
	status := c.serverManager.GetStatus()

	// 如果没有运行中的服务端，返回错误
	if status.RunningCount == 0 {
		return 0, fmt.Errorf("没有可用的服务端实例")
	}

	// 简单的负载均衡：选择第一个运行中的服务端
	for port, serverStatus := range status.Servers {
		if serverStatus.IsRunning {
			c.logger.WithField("selected_port", port).Debug("选择服务端进行测试")
			return port, nil
		}
	}

	return 0, fmt.Errorf("没有找到运行中的服务端")
}

// handleServerFailure 处理服务端故障
func (c *PreemptiveTestCoordinator) handleServerFailure(port int, err error) {
	c.logger.WithFields(logrus.Fields{
		"port":  port,
		"error": err.Error(),
	}).Warn("检测到服务端故障")

	// 尝试重启服务端
	if restartErr := c.serverManager.StopServer(port); restartErr != nil {
		c.logger.WithFields(logrus.Fields{
			"port":  port,
			"error": restartErr.Error(),
		}).Error("停止故障服务端失败")
	}

	// 等待一段时间后重新启动
	time.Sleep(2 * time.Second)

	if restartErr := c.serverManager.StartServer(port); restartErr != nil {
		c.logger.WithFields(logrus.Fields{
			"port":  port,
			"error": restartErr.Error(),
		}).Error("重启服务端失败")
	} else {
		c.logger.WithField("port", port).Info("服务端重启成功")
	}
}

// ExecuteTestBatch 为一批客户端执行测试（实现TestCoordinator接口）
func (c *PreemptiveTestCoordinator) ExecuteTestBatch(ctx context.Context, clients []client.Client, testConfig TestBatchConfig) (*TestBatchResult, error) {
	c.logger.WithFields(logrus.Fields{
		"client_count": len(clients),
		"test_config":  testConfig,
	}).Info("执行测试批次")

	// 确保服务端可用性
	if err := c.ensureServerAvailability(ctx); err != nil {
		return nil, fmt.Errorf("服务端资源检查失败: %w", err)
	}

	// 创建测试轮次
	round := &PreemptiveTestRound{
		ID:           uuid.New().String(),
		Hour:         time.Now().Hour(),
		StartTime:    time.Now(),
		Status:       "running",
		TotalClients: len(clients),
		TestResults:  make(map[string]*client.TestResult),
		Errors:       make(map[string]string),
	}

	// 执行抢占式测试
	if err := c.executePreemptiveTests(ctx, clients, round); err != nil {
		round.Status = "failed"
		round.EndTime = &[]time.Time{time.Now()}[0]

		return &TestBatchResult{
			BatchID:     round.ID,
			StartTime:   round.StartTime,
			EndTime:     *round.EndTime,
			Duration:    round.EndTime.Sub(round.StartTime),
			ClientCount: len(clients),
			TCPResults:  []client.TestResult{},
			UDPResults:  []client.TestResult{},
			Errors:      []TestError{},
		}, err
	}

	// 标记完成
	round.Status = "completed"
	round.EndTime = &[]time.Time{time.Now()}[0]
	round.CompletedClients = len(round.TestResults)
	round.FailedClients = len(round.Errors)

	// 分离TCP和UDP结果
	var tcpResults, udpResults []client.TestResult
	var errors []TestError

	for _, result := range round.TestResults {
		// 简单地将所有结果归类为TCP（可以根据实际需要调整）
		tcpResults = append(tcpResults, *result)
	}

	for clientID, errMsg := range round.Errors {
		errors = append(errors, TestError{
			ClientID:  clientID,
			TestType:  "tcp",
			Error:     errMsg,
			Timestamp: time.Now(),
		})
	}

	return &TestBatchResult{
		BatchID:     round.ID,
		StartTime:   round.StartTime,
		EndTime:     *round.EndTime,
		Duration:    round.EndTime.Sub(round.StartTime),
		ClientCount: len(clients),
		TCPResults:  tcpResults,
		UDPResults:  udpResults,
		Errors:      errors,
	}, nil
}

// ExecuteTestRound 为当前小时执行一个完整的测试回合（实现TestCoordinator接口）
func (c *PreemptiveTestCoordinator) ExecuteTestRound(ctx context.Context, hour int) (*TestRoundResult, error) {
	c.logger.WithField("hour", hour).Info("执行测试回合")

	// 确保服务端可用性
	if err := c.ensureServerAvailability(ctx); err != nil {
		return nil, fmt.Errorf("服务端资源检查失败: %w", err)
	}

	// 获取启用的客户端
	clients := c.clientManager.GetEnabledClients()
	if len(clients) == 0 {
		return nil, fmt.Errorf("没有可用的客户端")
	}

	// 创建测试轮次
	round := &PreemptiveTestRound{
		ID:           uuid.New().String(),
		Hour:         hour,
		StartTime:    time.Now(),
		Status:       "running",
		TotalClients: len(clients),
		TestResults:  make(map[string]*client.TestResult),
		Errors:       make(map[string]string),
	}

	c.mu.Lock()
	c.currentRound = round
	c.mu.Unlock()

	// 执行抢占式测试
	if err := c.executePreemptiveTests(ctx, clients, round); err != nil {
		round.Status = "failed"
		round.EndTime = &[]time.Time{time.Now()}[0]

		c.mu.Lock()
		c.failedRounds++
		c.lastError = err
		c.mu.Unlock()

		return &TestRoundResult{
			RoundID:      round.ID,
			Hour:         hour,
			StartTime:    round.StartTime,
			EndTime:      *round.EndTime,
			Duration:     round.EndTime.Sub(round.StartTime),
			BatchResults: []TestBatchResult{},
			Summary: TestRoundSummary{
				TCPSummary: TestTypeSummary{},
				UDPSummary: TestTypeSummary{},
			},
		}, err
	}

	// 标记完成
	round.Status = "completed"
	round.EndTime = &[]time.Time{time.Now()}[0]
	round.CompletedClients = len(round.TestResults)
	round.FailedClients = len(round.Errors)

	c.mu.Lock()
	c.totalRounds++
	c.successfulRounds++
	c.currentRound = nil
	c.mu.Unlock()

	return &TestRoundResult{
		RoundID:   round.ID,
		Hour:      hour,
		StartTime: round.StartTime,
		EndTime:   *round.EndTime,
		Duration:  round.EndTime.Sub(round.StartTime),
		BatchResults: []TestBatchResult{
			{
				BatchID:     round.ID,
				StartTime:   round.StartTime,
				EndTime:     *round.EndTime,
				Duration:    round.EndTime.Sub(round.StartTime),
				ClientCount: len(clients),
				TCPResults:  []client.TestResult{},
				UDPResults:  []client.TestResult{},
				Errors:      []TestError{},
			},
		},
		Summary: TestRoundSummary{
			TCPSummary: TestTypeSummary{},
			UDPSummary: TestTypeSummary{},
		},
	}, nil
}
