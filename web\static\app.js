// iperf3 控制器前端应用
class Iperf3Controller {
    constructor() {
        this.apiBase = '/api/v1';
        this.refreshInterval = 5000; // 5秒刷新一次
        this.refreshTimer = null;
        
        this.init();
    }
    
    init() {
        console.log('🚀 iperf3 控制器初始化...');
        this.startAutoRefresh();
        this.loadInitialData();
        this.initWebSocket(); // 初始化WebSocket
        this.enhanceUserExperience(); // 增强用户体验
    }
    
    // 开始自动刷新
    startAutoRefresh() {
        this.refreshTimer = setInterval(() => {
            this.refreshData();
        }, this.refreshInterval);
    }
    
    // 停止自动刷新
    stopAutoRefresh() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
        }
    }
    
    // 加载初始数据
    async loadInitialData() {
        await this.refreshData();
    }
    
    // 刷新所有数据
    async refreshData() {
        try {
            await Promise.all([
                this.loadSystemStatus(),
                this.loadPerformanceStats(),
                this.loadClients(),
                this.loadScheduleStatus(),
                this.loadSyncStatus()
            ]);
        } catch (error) {
            console.error('刷新数据失败:', error);
        }
    }
    
    // 加载系统状态
    async loadSystemStatus() {
        try {
            const response = await fetch(`${this.apiBase}/status`);
            const data = await response.json();
            
            if (data.success) {
                this.updateSystemStatus(data.data);
            }
        } catch (error) {
            console.error('加载系统状态失败:', error);
        }
    }
    
    // 更新系统状态显示
    updateSystemStatus(data) {
        const components = data.components;
        
        // 更新调度器状态
        const schedulerStatus = document.getElementById('scheduler-status');
        const schedulerText = document.getElementById('scheduler-text');
        if (components.scheduler.running) {
            schedulerStatus.className = 'status-indicator status-running';
            schedulerText.textContent = '运行中';
        } else {
            schedulerStatus.className = 'status-indicator status-stopped';
            schedulerText.textContent = '已停止';
        }
        
        // 更新同步状态
        const syncStatus = document.getElementById('sync-status');
        const syncText = document.getElementById('sync-text');
        if (components.sync.running) {
            syncStatus.className = 'status-indicator status-running';
            syncText.textContent = '运行中';
        } else {
            syncStatus.className = 'status-indicator status-stopped';
            syncText.textContent = '已停止';
        }
        
        // 更新对端连接状态
        const peerStatus = document.getElementById('peer-status');
        const peerText = document.getElementById('peer-text');
        if (components.sync.peer_connected) {
            peerStatus.className = 'status-indicator status-running';
            peerText.textContent = '已连接';
        } else {
            peerStatus.className = 'status-indicator status-warning';
            peerText.textContent = '未连接';
        }
        
        // 更新数据同步状态
        const dataSyncStatus = document.getElementById('data-sync-status');
        dataSyncStatus.textContent = components.sync.data_in_sync ? '已同步' : '未同步';
        
        // 更新测试统计
        document.getElementById('total-tests').textContent = components.scheduler.total_tests || 0;
        document.getElementById('successful-tests').textContent = components.scheduler.successful_tests || 0;
        document.getElementById('failed-tests').textContent = components.scheduler.failed_tests || 0;
        
        const totalTests = components.scheduler.total_tests || 0;
        const successfulTests = components.scheduler.successful_tests || 0;
        const successRate = totalTests > 0 ? ((successfulTests / totalTests) * 100).toFixed(1) : 0;
        document.getElementById('success-rate').textContent = `${successRate}%`;
        
        // 更新客户端数量
        document.getElementById('total-clients').textContent = components.clients.total_count || 0;
        document.getElementById('enabled-clients').textContent = components.clients.enabled_count || 0;
    }
    
    // 加载性能统计
    async loadPerformanceStats() {
        try {
            const response = await fetch(`${this.apiBase}/stats/performance`);
            const data = await response.json();
            
            if (data.success) {
                this.updatePerformanceStats(data.data);
            }
        } catch (error) {
            console.error('加载性能统计失败:', error);
        }
    }
    
    // 更新性能统计显示
    updatePerformanceStats(data) {
        const performance = data.performance;
        
        document.getElementById('avg-tcp-speed').textContent = `${performance.avg_tcp_speed} Mbps`;
        document.getElementById('avg-udp-speed').textContent = `${performance.avg_udp_speed} Mbps`;
        document.getElementById('max-tcp-speed').textContent = `${performance.max_tcp_speed} Mbps`;
        document.getElementById('avg-latency').textContent = `${performance.avg_latency} ms`;
        document.getElementById('packet-loss').textContent = `${performance.packet_loss_rate}%`;
    }
    
    // 加载客户端列表
    async loadClients() {
        try {
            const response = await fetch(`${this.apiBase}/clients`);
            const data = await response.json();
            
            if (data.success) {
                this.updateClientsDisplay(data.data.clients);
            }
        } catch (error) {
            console.error('加载客户端列表失败:', error);
        }
    }
    
    // 更新客户端显示
    updateClientsDisplay(clients) {
        const clientsGrid = document.getElementById('clients-grid');
        clientsGrid.innerHTML = '';
        
        clients.forEach(client => {
            const clientCard = document.createElement('div');
            clientCard.className = 'client-card';
            clientCard.innerHTML = `
                <div class="client-name">${client.name}</div>
                <div class="client-info">${client.host}:${client.port}</div>
                <div class="client-info">状态: ${client.status}</div>
            `;
            clientsGrid.appendChild(clientCard);
        });
    }
    
    // 加载调度状态
    async loadScheduleStatus() {
        try {
            const response = await fetch(`${this.apiBase}/schedule/status`);
            const data = await response.json();
            
            if (data.success) {
                this.updateScheduleStatus(data.data);
            }
        } catch (error) {
            console.error('加载调度状态失败:', error);
        }
    }
    
    // 更新调度状态显示
    updateScheduleStatus(data) {
        if (data.next_test_time) {
            const nextTime = new Date(data.next_test_time);
            document.getElementById('next-test-time').textContent = nextTime.toLocaleTimeString();
        } else {
            document.getElementById('next-test-time').textContent = '--';
        }
    }
    
    // 加载同步状态
    async loadSyncStatus() {
        try {
            const response = await fetch(`${this.apiBase}/sync/status`);
            const data = await response.json();
            
            if (data.success) {
                // 同步状态已在系统状态中更新
            }
        } catch (error) {
            console.error('加载同步状态失败:', error);
        }
    }
    
    // 显示提示消息
    showAlert(message, type = 'success') {
        const alertContainer = document.getElementById('alert-container');
        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.textContent = message;
        
        alertContainer.appendChild(alert);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 3000);
    }
    
    // API调用辅助方法
    async apiCall(endpoint, method = 'GET', body = null) {
        try {
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };
            
            if (body) {
                options.body = JSON.stringify(body);
            }
            
            const response = await fetch(`${this.apiBase}${endpoint}`, options);
            const data = await response.json();
            
            return data;
        } catch (error) {
            console.error(`API调用失败 ${endpoint}:`, error);
            throw error;
        }
    }

    // 实时数据推送
    initWebSocket() {
        if ('WebSocket' in window) {
            this.ws = new WebSocket(`ws://${window.location.host}/ws`);
            
            this.ws.onopen = () => {
                console.log('WebSocket连接已建立');
                this.showAlert('实时连接已建立', 'success');
            };
            
            this.ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleRealtimeUpdate(data);
                } catch (error) {
                    console.error('解析实时数据失败:', error);
                }
            };
            
            this.ws.onclose = () => {
                console.log('WebSocket连接已关闭');
                this.showAlert('实时连接已断开', 'warning');
                // 5秒后重连
                setTimeout(() => this.initWebSocket(), 5000);
            };
            
            this.ws.onerror = (error) => {
                console.error('WebSocket错误:', error);
                this.showAlert('实时连接错误', 'error');
            };
        }
    }

    // 处理实时数据更新
    handleRealtimeUpdate(data) {
        switch (data.type) {
            case 'test_progress':
                this.updateTestProgress(data.data);
                break;
            case 'client_status':
                this.updateClientStatus(data.data);
                break;
            case 'performance_update':
                this.updatePerformanceStats(data.data);
                break;
            case 'system_alert':
                this.showAlert(data.data.message, data.data.type);
                break;
        }
    }

    // 更新测试进度
    updateTestProgress(progress) {
        const progressBar = document.getElementById('test-progress');
        if (progressBar) {
            progressBar.style.width = `${progress.percentage}%`;
            progressBar.textContent = `${progress.percentage}%`;
        }
    }

    // 更新客户端状态
    updateClientStatus(clientData) {
        const clientElement = document.querySelector(`[data-client-id="${clientData.id}"]`);
        if (clientElement) {
            const statusElement = clientElement.querySelector('.client-status');
            if (statusElement) {
                statusElement.textContent = clientData.status;
                statusElement.className = `client-status status-${clientData.status}`;
            }
        }
    }

    // 创建交互式图表
    createInteractiveChart(containerId, data, options = {}) {
        const container = document.getElementById(containerId);
        if (!container) return;

        // 使用Chart.js创建交互式图表
        const ctx = container.getContext('2d');
        const chart = new Chart(ctx, {
            type: options.type || 'line',
            data: data,
            options: {
                responsive: true,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                plugins: {
                    title: {
                        display: true,
                        text: options.title || '性能指标'
                    },
                    legend: {
                        display: true
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        return chart;
    }

    // 用户体验优化
    enhanceUserExperience() {
        // 添加加载动画
        this.addLoadingAnimations();
        
        // 添加键盘快捷键
        this.addKeyboardShortcuts();
        
        // 添加主题切换
        this.addThemeToggle();
        
        // 添加通知系统
        this.addNotificationSystem();
    }

    // 添加加载动画
    addLoadingAnimations() {
        const loadingElements = document.querySelectorAll('.loading');
        loadingElements.forEach(element => {
            element.innerHTML = '<div class="spinner"></div>';
        });
    }

    // 添加键盘快捷键
    addKeyboardShortcuts() {
        document.addEventListener('keydown', (event) => {
            if (event.ctrlKey || event.metaKey) {
                switch (event.key) {
                    case 'r':
                        event.preventDefault();
                        this.refreshData();
                        break;
                    case 's':
                        event.preventDefault();
                        if (event.shiftKey) {
                            stopScheduler();
                        } else {
                            startScheduler();
                        }
                        break;
                    case 't':
                        event.preventDefault();
                        triggerTest();
                        break;
                }
            }
        });
    }

    // 添加主题切换
    addThemeToggle() {
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                document.body.classList.toggle('dark-theme');
                const isDark = document.body.classList.contains('dark-theme');
                localStorage.setItem('theme', isDark ? 'dark' : 'light');
            });
        }
    }

    // 添加通知系统
    addNotificationSystem() {
        if ('Notification' in window && Notification.permission === 'granted') {
            this.enableNotifications = true;
        } else if ('Notification' in window && Notification.permission !== 'denied') {
            Notification.requestPermission().then(permission => {
                this.enableNotifications = permission === 'granted';
            });
        }
    }

    // 发送通知
    sendNotification(title, body, icon = null) {
        if (this.enableNotifications) {
            new Notification(title, { body, icon });
        }
    }
}

// 全局控制器实例
let controller;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    controller = new Iperf3Controller();
});

// 控制面板函数
async function startScheduler() {
    try {
        const data = await controller.apiCall('/schedule/start', 'POST');
        if (data.success) {
            controller.showAlert('调度器启动成功', 'success');
            controller.refreshData();
        } else {
            controller.showAlert(`启动调度器失败: ${data.error}`, 'error');
        }
    } catch (error) {
        controller.showAlert('启动调度器失败', 'error');
    }
}

async function stopScheduler() {
    try {
        const data = await controller.apiCall('/schedule/stop', 'POST');
        if (data.success) {
            controller.showAlert('调度器停止成功', 'success');
            controller.refreshData();
        } else {
            controller.showAlert(`停止调度器失败: ${data.error}`, 'error');
        }
    } catch (error) {
        controller.showAlert('停止调度器失败', 'error');
    }
}

async function triggerTest() {
    try {
        const data = await controller.apiCall('/tests/trigger', 'POST');
        if (data.success) {
            controller.showAlert('手动测试已触发', 'success');
            controller.refreshData();
        } else {
            controller.showAlert(`触发测试失败: ${data.error}`, 'error');
        }
    } catch (error) {
        controller.showAlert('触发测试失败', 'error');
    }
}

async function startSync() {
    try {
        const data = await controller.apiCall('/sync/start', 'POST');
        if (data.success) {
            controller.showAlert('同步服务启动成功', 'success');
            controller.refreshData();
        } else {
            controller.showAlert(`启动同步服务失败: ${data.error}`, 'error');
        }
    } catch (error) {
        controller.showAlert('启动同步服务失败', 'error');
    }
}

async function stopSync() {
    try {
        const data = await controller.apiCall('/sync/stop', 'POST');
        if (data.success) {
            controller.showAlert('同步服务停止成功', 'success');
            controller.refreshData();
        } else {
            controller.showAlert(`停止同步服务失败: ${data.error}`, 'error');
        }
    } catch (error) {
        controller.showAlert('停止同步服务失败', 'error');
    }
}

async function refreshData() {
    await controller.refreshData();
    controller.showAlert('数据刷新完成', 'success');
}
