package config

import (
	"fmt"
	"net"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// Validate 验证配置
func (m *Manager) Validate() error {
	if m.config == nil {
		return fmt.Errorf("configuration is nil")
	}

	// 验证服务器配置
	if err := m.validateServer(); err != nil {
		return fmt.Errorf("server config validation failed: %w", err)
	}

	// 验证调度配置
	if err := m.validateSchedule(); err != nil {
		return fmt.Errorf("schedule config validation failed: %w", err)
	}

	// 验证对等配置
	if err := m.validatePeer(); err != nil {
		return fmt.Errorf("peer config validation failed: %w", err)
	}

	// 验证数据库配置
	if err := m.validateDatabase(); err != nil {
		return fmt.Errorf("database config validation failed: %w", err)
	}

	// 验证服务器列表
	if err := m.validateServers(); err != nil {
		return fmt.Errorf("servers config validation failed: %w", err)
	}

	// 验证性能配置
	if err := m.validatePerformance(); err != nil {
		return fmt.Errorf("performance config validation failed: %w", err)
	}

	// 验证客户端管理配置
	if err := m.validateClientManagement(); err != nil {
		return fmt.Errorf("client management config validation failed: %w", err)
	}

	// 验证同步配置
	if err := m.validateSync(); err != nil {
		return fmt.Errorf("sync config validation failed: %w", err)
	}

	// 验证日志配置
	if err := m.validateLogging(); err != nil {
		return fmt.Errorf("logging config validation failed: %w", err)
	}

	// 验证iperf3服务端配置
	if err := m.validateIperf3Server(); err != nil {
		return fmt.Errorf("iperf3 server config validation failed: %w", err)
	}

	return nil
}

// validateServer 验证服务器配置
func (m *Manager) validateServer() error {
	server := &m.config.Server

	// 验证监听端口
	if server.ListenPort <= 0 || server.ListenPort > 65535 {
		return fmt.Errorf("invalid listen port: %d", server.ListenPort)
	}

	// 验证 Web 端口
	if server.WebPort <= 0 || server.WebPort > 65535 {
		return fmt.Errorf("invalid web port: %d", server.WebPort)
	}

	// 确保端口不同
	if server.ListenPort == server.WebPort {
		return fmt.Errorf("listen port and web port cannot be the same: %d", server.ListenPort)
	}

	return nil
}

// validateSchedule 验证调度配置
func (m *Manager) validateSchedule() error {
	schedule := &m.config.Schedule

	// 验证模式
	validModes := []string{"odd", "even", "always"}
	if !contains(validModes, schedule.Mode) {
		return fmt.Errorf("invalid schedule mode: %s, must be one of: %s",
			schedule.Mode, strings.Join(validModes, ", "))
	}

	// 验证时区
	if schedule.Timezone == "" {
		return fmt.Errorf("timezone cannot be empty")
	}

	// 尝试加载时区
	if _, err := time.LoadLocation(schedule.Timezone); err != nil {
		return fmt.Errorf("invalid timezone: %s", schedule.Timezone)
	}

	return nil
}

// validatePeer 验证对等配置
func (m *Manager) validatePeer() error {
	peer := &m.config.Peer

	// 验证 IP 地址（如果提供）
	if peer.IP != "" {
		if net.ParseIP(peer.IP) == nil {
			return fmt.Errorf("invalid peer IP address: %s", peer.IP)
		}
	}

	// 验证端口
	if peer.Port <= 0 || peer.Port > 65535 {
		return fmt.Errorf("invalid peer port: %d", peer.Port)
	}

	// 验证同步间隔
	if peer.SyncInterval <= 0 {
		return fmt.Errorf("sync interval must be positive: %v", peer.SyncInterval)
	}

	return nil
}

// validateDatabase 验证数据库配置
func (m *Manager) validateDatabase() error {
	db := &m.config.Database

	// 验证路径
	if db.Path == "" {
		return fmt.Errorf("database path cannot be empty")
	}

	// 确保目录存在或可以创建
	dir := filepath.Dir(db.Path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("cannot create database directory %s: %w", dir, err)
	}

	// 验证备份间隔
	if db.BackupInterval <= 0 {
		return fmt.Errorf("backup interval must be positive: %v", db.BackupInterval)
	}

	// 验证最大大小
	if db.MaxSizeMB <= 0 {
		return fmt.Errorf("max size must be positive: %d", db.MaxSizeMB)
	}

	// 验证保留天数
	if db.RetentionDays <= 0 {
		return fmt.Errorf("retention days must be positive: %d", db.RetentionDays)
	}

	// 验证连接池设置
	if db.MaxOpenConns <= 0 {
		return fmt.Errorf("max open connections must be positive: %d", db.MaxOpenConns)
	}

	if db.MaxIdleConns < 0 || db.MaxIdleConns > db.MaxOpenConns {
		return fmt.Errorf("max idle connections must be between 0 and max open connections: %d", db.MaxIdleConns)
	}

	// 验证超时
	if db.ConnMaxLifetime <= 0 {
		return fmt.Errorf("connection max lifetime must be positive: %v", db.ConnMaxLifetime)
	}

	if db.ConnMaxIdleTime <= 0 {
		return fmt.Errorf("connection max idle time must be positive: %v", db.ConnMaxIdleTime)
	}

	if db.BusyTimeout <= 0 {
		return fmt.Errorf("busy timeout must be positive: %v", db.BusyTimeout)
	}

	// 验证日志模式
	validJournalModes := []string{"DELETE", "TRUNCATE", "PERSIST", "MEMORY", "WAL", "OFF"}
	if !contains(validJournalModes, db.JournalMode) {
		return fmt.Errorf("invalid journal mode: %s, must be one of: %s",
			db.JournalMode, strings.Join(validJournalModes, ", "))
	}

	// 验证同步模式
	validSyncModes := []string{"OFF", "NORMAL", "FULL", "EXTRA"}
	if !contains(validSyncModes, db.Synchronous) {
		return fmt.Errorf("invalid synchronous mode: %s, must be one of: %s",
			db.Synchronous, strings.Join(validSyncModes, ", "))
	}

	return nil
}

// validateServers 验证服务器列表
func (m *Manager) validateServers() error {
	if len(m.config.Servers) == 0 {
		return fmt.Errorf("at least one server must be configured")
	}

	serverNames := make(map[string]bool)
	for i, server := range m.config.Servers {
		// 验证名称
		if server.Name == "" {
			return fmt.Errorf("server %d: name cannot be empty", i)
		}

		// 检查重复名称
		if serverNames[server.Name] {
			return fmt.Errorf("duplicate server name: %s", server.Name)
		}
		serverNames[server.Name] = true

		// 验证主机
		if server.Host == "" {
			return fmt.Errorf("server %s: host cannot be empty", server.Name)
		}

		// 验证 IP 地址或主机名
		if net.ParseIP(server.Host) == nil {
			// If not an IP, try to resolve hostname
			if _, err := net.LookupHost(server.Host); err != nil {
				m.logger.WithField("server", server.Name).WithField("host", server.Host).
					Warn("Cannot resolve hostname, but continuing anyway")
			}
		}

		// 验证端口
		if server.Port <= 0 || server.Port > 65535 {
			return fmt.Errorf("server %s: invalid port: %d", server.Name, server.Port)
		}
	}

	return nil
}

// validatePerformance 验证性能配置
func (m *Manager) validatePerformance() error {
	perf := &m.config.Performance

	// 验证 TCP 配置
	if perf.TCP.ParallelStreams <= 0 {
		return fmt.Errorf("TCP parallel streams must be positive: %d", perf.TCP.ParallelStreams)
	}

	if perf.TCP.MSS <= 0 {
		return fmt.Errorf("TCP MSS must be positive: %d", perf.TCP.MSS)
	}

	if perf.TCP.Duration <= 0 {
		return fmt.Errorf("TCP duration must be positive: %v", perf.TCP.Duration)
	}

	// 验证 UDP 配置
	if perf.UDP.PacketSize <= 0 {
		return fmt.Errorf("UDP packet size must be positive: %d", perf.UDP.PacketSize)
	}

	if perf.UDP.Duration <= 0 {
		return fmt.Errorf("UDP duration must be positive: %v", perf.UDP.Duration)
	}

	// 验证并发配置
	if perf.Concurrency.MaxWorkers <= 0 {
		return fmt.Errorf("max workers must be positive: %d", perf.Concurrency.MaxWorkers)
	}

	if perf.Concurrency.BatchSize <= 0 {
		return fmt.Errorf("batch size must be positive: %d", perf.Concurrency.BatchSize)
	}

	if perf.Concurrency.BatchDelay < 0 {
		return fmt.Errorf("batch delay cannot be negative: %v", perf.Concurrency.BatchDelay)
	}

	return nil
}

// validateClientManagement 验证客户端管理配置
func (m *Manager) validateClientManagement() error {
	cm := &m.config.ClientManagement

	if cm.PrepareTimeout <= 0 {
		return fmt.Errorf("prepare timeout must be positive: %v", cm.PrepareTimeout)
	}

	if cm.TestTimeout <= 0 {
		return fmt.Errorf("test timeout must be positive: %v", cm.TestTimeout)
	}

	if cm.StopTimeout <= 0 {
		return fmt.Errorf("stop timeout must be positive: %v", cm.StopTimeout)
	}

	if cm.RetryAttempts < 0 {
		return fmt.Errorf("retry attempts cannot be negative: %d", cm.RetryAttempts)
	}

	if cm.RetryDelay < 0 {
		return fmt.Errorf("retry delay cannot be negative: %v", cm.RetryDelay)
	}

	return nil
}

// validateSync 验证同步配置
func (m *Manager) validateSync() error {
	sync := &m.config.Sync

	if sync.Interval <= 0 {
		return fmt.Errorf("sync interval must be positive: %v", sync.Interval)
	}

	if sync.Timeout <= 0 {
		return fmt.Errorf("sync timeout must be positive: %v", sync.Timeout)
	}

	return nil
}

// validateLogging 验证日志配置
func (m *Manager) validateLogging() error {
	log := &m.config.Logging

	// 验证级别
	validLevels := []string{"trace", "debug", "info", "warn", "error", "fatal", "panic"}
	if !contains(validLevels, log.Level) {
		return fmt.Errorf("invalid log level: %s, must be one of: %s",
			log.Level, strings.Join(validLevels, ", "))
	}

	// 验证格式
	validFormats := []string{"text", "json"}
	if !contains(validFormats, log.Format) {
		return fmt.Errorf("invalid log format: %s, must be one of: %s",
			log.Format, strings.Join(validFormats, ", "))
	}

	// 验证输出
	validOutputs := []string{"stdout", "stderr", "file"}
	if !contains(validOutputs, log.Output) {
		return fmt.Errorf("invalid log output: %s, must be one of: %s",
			log.Output, strings.Join(validOutputs, ", "))
	}

	// 如果输出是文件，验证文件路径
	if log.Output == "file" {
		if log.FilePath == "" {
			return fmt.Errorf("log file path cannot be empty when output is file")
		}

		// 确保目录存在或可以创建
		dir := filepath.Dir(log.FilePath)
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("cannot create log directory %s: %w", dir, err)
		}
	}

	// 验证轮转设置
	if log.MaxSize <= 0 {
		return fmt.Errorf("log max size must be positive: %d", log.MaxSize)
	}

	if log.MaxBackups < 0 {
		return fmt.Errorf("log max backups cannot be negative: %d", log.MaxBackups)
	}

	if log.MaxAge < 0 {
		return fmt.Errorf("log max age cannot be negative: %d", log.MaxAge)
	}

	return nil
}

// validateIperf3Server 验证iperf3服务端配置
func (m *Manager) validateIperf3Server() error {
	server := &m.config.Iperf3Server

	// 验证二进制文件路径
	if server.BinaryPath == "" {
		return fmt.Errorf("iperf3 binary path cannot be empty")
	}

	// 验证端口
	if server.Port <= 0 || server.Port > 65535 {
		return fmt.Errorf("invalid iperf3 server port: %d", server.Port)
	}

	// 验证自动关闭超时
	if server.AutoShutdownTimeout <= 0 {
		return fmt.Errorf("auto shutdown timeout must be positive: %v", server.AutoShutdownTimeout)
	}

	// 验证最大连接数
	if server.MaxConnections <= 0 {
		return fmt.Errorf("max connections must be positive: %d", server.MaxConnections)
	}

	return nil
}

// contains 检查切片是否包含字符串
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
