package iperf3

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"sync"
	"syscall"
	"time"

	"iperf3-controller/internal/config"

	"github.com/sirupsen/logrus"
)

// ServerManager iperf3服务端管理器接口
type ServerManager interface {
	// Start 启动服务端管理器
	Start(ctx context.Context) error

	// Stop 停止服务端管理器
	Stop() error

	// StartServer 启动指定端口的iperf3服务端
	StartServer(port int) error

	// StopServer 停止指定端口的iperf3服务端
	StopServer(port int) error

	// GetStatus 获取服务端管理器状态
	GetStatus() *ServerStatus

	// IsServerRunning 检查指定端口的服务端是否运行
	IsServerRunning(port int) bool

	// GetServerStatus 获取指定端口服务端的状态
	GetServerStatus(port int) *ServerInstanceStatus
}

// ServerStatus 服务端管理器状态
type ServerStatus struct {
	IsRunning    bool                          `json:"is_running"`
	TotalServers int                           `json:"total_servers"`
	RunningCount int                           `json:"running_count"`
	Servers      map[int]*ServerInstanceStatus `json:"servers"`
	LastUpdate   time.Time                     `json:"last_update"`
}

// ServerInstanceStatus 服务端实例状态
type ServerInstanceStatus struct {
	Port      int       `json:"port"`
	PID       int       `json:"pid"`
	IsRunning bool      `json:"is_running"`
	StartTime time.Time `json:"start_time"`
	LogFile   string    `json:"log_file"`
	LastCheck time.Time `json:"last_check"`
	Error     string    `json:"error,omitempty"`
}

// DefaultServerManager 默认的iperf3服务端管理器实现
type DefaultServerManager struct {
	config *config.Iperf3ServerConfig
	logger *logrus.Logger

	// 状态管理
	isRunning bool
	servers   map[int]*ServerInstanceStatus
	mu        sync.RWMutex

	// 控制通道
	stopChan chan struct{}

	// 健康检查定时器
	healthTicker *time.Ticker
}

// NewServerManager 创建新的iperf3服务端管理器
func NewServerManager(config *config.Iperf3ServerConfig, logger *logrus.Logger) ServerManager {
	if logger == nil {
		logger = logrus.New()
	}

	return &DefaultServerManager{
		config:   config,
		logger:   logger,
		servers:  make(map[int]*ServerInstanceStatus),
		stopChan: make(chan struct{}),
	}
}

// Start 启动服务端管理器
func (sm *DefaultServerManager) Start(ctx context.Context) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	if sm.isRunning {
		return fmt.Errorf("server manager is already running")
	}

	sm.logger.Info("启动iperf3服务端管理器")

	sm.isRunning = true

	// 启动健康检查
	sm.healthTicker = time.NewTicker(30 * time.Second)
	go sm.healthCheckRoutine(ctx)

	sm.logger.Info("iperf3服务端管理器启动成功")
	return nil
}

// Stop 停止服务端管理器
func (sm *DefaultServerManager) Stop() error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	if !sm.isRunning {
		return nil
	}

	sm.logger.Info("停止iperf3服务端管理器")

	// 停止健康检查
	if sm.healthTicker != nil {
		sm.healthTicker.Stop()
	}

	// 发送停止信号
	close(sm.stopChan)

	// 停止所有运行中的服务端
	for port := range sm.servers {
		if err := sm.stopServerInternal(port); err != nil {
			sm.logger.WithFields(logrus.Fields{
				"port":  port,
				"error": err.Error(),
			}).Error("停止服务端失败")
		}
	}

	sm.isRunning = false
	sm.logger.Info("iperf3服务端管理器已停止")
	return nil
}

// StartServer 启动指定端口的iperf3服务端
func (sm *DefaultServerManager) StartServer(port int) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	return sm.startServerInternal(port)
}

// startServerInternal 内部启动服务端方法（需要持有锁）
func (sm *DefaultServerManager) startServerInternal(port int) error {
	// 检查是否已经运行
	if server, exists := sm.servers[port]; exists && server.IsRunning {
		return fmt.Errorf("server on port %d is already running", port)
	}

	// 创建日志文件路径
	logFile := filepath.Join("/tmp", fmt.Sprintf("iperf3-server-%d.log", port))

	// 构建iperf3服务端命令
	args := []string{
		"-s",                     // 服务端模式
		"-p", strconv.Itoa(port), // 端口
		"-D",                 // 后台运行
		"--logfile", logFile, // 日志文件
	}

	sm.logger.WithFields(logrus.Fields{
		"port":     port,
		"command":  sm.config.BinaryPath,
		"args":     args,
		"log_file": logFile,
	}).Info("启动iperf3服务端")

	// 执行命令
	cmd := exec.Command(sm.config.BinaryPath, args...)
	if err := cmd.Start(); err != nil {
		return fmt.Errorf("failed to start iperf3 server on port %d: %w", port, err)
	}

	// 等待进程启动
	time.Sleep(100 * time.Millisecond)

	// 检查进程是否成功启动
	if err := cmd.Process.Signal(syscall.Signal(0)); err != nil {
		return fmt.Errorf("iperf3 server process failed to start on port %d: %w", port, err)
	}

	// 记录服务端状态
	sm.servers[port] = &ServerInstanceStatus{
		Port:      port,
		PID:       cmd.Process.Pid,
		IsRunning: true,
		StartTime: time.Now(),
		LogFile:   logFile,
		LastCheck: time.Now(),
	}

	sm.logger.WithFields(logrus.Fields{
		"port": port,
		"pid":  cmd.Process.Pid,
	}).Info("iperf3服务端启动成功")

	return nil
}

// StopServer 停止指定端口的iperf3服务端
func (sm *DefaultServerManager) StopServer(port int) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	return sm.stopServerInternal(port)
}

// stopServerInternal 内部停止服务端方法（需要持有锁）
func (sm *DefaultServerManager) stopServerInternal(port int) error {
	server, exists := sm.servers[port]
	if !exists || !server.IsRunning {
		return fmt.Errorf("server on port %d is not running", port)
	}

	sm.logger.WithFields(logrus.Fields{
		"port": port,
		"pid":  server.PID,
	}).Info("停止iperf3服务端")

	// 查找并终止进程
	if err := sm.killServerProcess(server.PID); err != nil {
		sm.logger.WithFields(logrus.Fields{
			"port":  port,
			"pid":   server.PID,
			"error": err.Error(),
		}).Error("终止iperf3服务端进程失败")
		return fmt.Errorf("failed to kill iperf3 server process (PID: %d): %w", server.PID, err)
	}

	// 更新状态
	server.IsRunning = false
	server.LastCheck = time.Now()

	sm.logger.WithFields(logrus.Fields{
		"port": port,
		"pid":  server.PID,
	}).Info("iperf3服务端已停止")

	return nil
}

// killServerProcess 终止服务端进程
func (sm *DefaultServerManager) killServerProcess(pid int) error {
	process, err := os.FindProcess(pid)
	if err != nil {
		return fmt.Errorf("failed to find process %d: %w", pid, err)
	}

	// 先尝试优雅关闭
	if err := process.Signal(syscall.SIGTERM); err != nil {
		// 如果优雅关闭失败，强制终止
		if err := process.Signal(syscall.SIGKILL); err != nil {
			return fmt.Errorf("failed to kill process %d: %w", pid, err)
		}
	}

	// 等待进程结束
	_, err = process.Wait()
	return err
}

// GetStatus 获取服务端管理器状态
func (sm *DefaultServerManager) GetStatus() *ServerStatus {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	runningCount := 0
	servers := make(map[int]*ServerInstanceStatus)

	for port, server := range sm.servers {
		// 复制服务端状态
		serverCopy := *server
		servers[port] = &serverCopy

		if server.IsRunning {
			runningCount++
		}
	}

	return &ServerStatus{
		IsRunning:    sm.isRunning,
		TotalServers: len(sm.servers),
		RunningCount: runningCount,
		Servers:      servers,
		LastUpdate:   time.Now(),
	}
}

// IsServerRunning 检查指定端口的服务端是否运行
func (sm *DefaultServerManager) IsServerRunning(port int) bool {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	server, exists := sm.servers[port]
	return exists && server.IsRunning
}

// GetServerStatus 获取指定端口服务端的状态
func (sm *DefaultServerManager) GetServerStatus(port int) *ServerInstanceStatus {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	server, exists := sm.servers[port]
	if !exists {
		return nil
	}

	// 返回副本
	serverCopy := *server
	return &serverCopy
}

// healthCheckRoutine 健康检查协程
func (sm *DefaultServerManager) healthCheckRoutine(ctx context.Context) {
	sm.logger.Debug("启动iperf3服务端健康检查协程")

	for {
		select {
		case <-ctx.Done():
			sm.logger.Debug("健康检查协程收到上下文取消信号")
			return
		case <-sm.stopChan:
			sm.logger.Debug("健康检查协程收到停止信号")
			return
		case <-sm.healthTicker.C:
			sm.performHealthCheck()
		}
	}
}

// performHealthCheck 执行健康检查
func (sm *DefaultServerManager) performHealthCheck() {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	sm.logger.Debug("执行iperf3服务端健康检查")

	for port, server := range sm.servers {
		if !server.IsRunning {
			continue
		}

		// 检查进程是否还在运行
		if !sm.isProcessRunning(server.PID) {
			sm.logger.WithFields(logrus.Fields{
				"port": port,
				"pid":  server.PID,
			}).Warn("检测到iperf3服务端进程已停止")

			server.IsRunning = false
			server.Error = "进程意外停止"
			server.LastCheck = time.Now()

			// 如果启用了动态模式且自动重启，可以在这里重启
			if sm.config.DynamicMode {
				sm.logger.WithField("port", port).Info("动态模式下尝试重启服务端")
				if err := sm.startServerInternal(port); err != nil {
					sm.logger.WithFields(logrus.Fields{
						"port":  port,
						"error": err.Error(),
					}).Error("自动重启服务端失败")
				}
			}
		} else {
			server.LastCheck = time.Now()
			server.Error = ""
		}
	}
}

// isProcessRunning 检查进程是否运行
func (sm *DefaultServerManager) isProcessRunning(pid int) bool {
	process, err := os.FindProcess(pid)
	if err != nil {
		return false
	}

	// 发送信号0检查进程是否存在
	err = process.Signal(syscall.Signal(0))
	return err == nil
}

// StartServerWithTimeout 启动服务端并在指定时间后自动停止
func (sm *DefaultServerManager) StartServerWithTimeout(port int, timeout time.Duration) error {
	if err := sm.StartServer(port); err != nil {
		return err
	}

	// 如果启用了自动关闭，设置定时器
	if sm.config.DynamicMode && timeout > 0 {
		go func() {
			time.Sleep(timeout)
			if err := sm.StopServer(port); err != nil {
				sm.logger.WithFields(logrus.Fields{
					"port":  port,
					"error": err.Error(),
				}).Error("自动停止服务端失败")
			} else {
				sm.logger.WithField("port", port).Info("服务端已自动停止")
			}
		}()
	}

	return nil
}
