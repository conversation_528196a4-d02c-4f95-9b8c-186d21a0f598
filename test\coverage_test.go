package test

import (
	"testing"
	"time"

	"iperf3-controller/internal/api"
	"iperf3-controller/internal/client"
	"iperf3-controller/internal/config"
	"iperf3-controller/internal/coordinator"
	"iperf3-controller/internal/database"
	"iperf3-controller/internal/scheduler"
	"iperf3-controller/internal/sync"

	"github.com/sirupsen/logrus"
)

// TestDatabaseCoverage 数据库模块测试覆盖
func TestDatabaseCoverage(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	// 测试数据库连接
	dbConfig := database.DefaultConfig()
	dbConfig.Path = ":memory:" // 使用内存数据库进行测试

	db, err := database.New(dbConfig, logger)
	if err != nil {
		t.Fatalf("创建数据库失败: %v", err)
	}
	defer db.Close()

	// 测试数据库迁移
	if err := db.Migrate(); err != nil {
		t.<PERSON>alf("数据库迁移失败: %v", err)
	}

	// 测试数据库统计
	stats, err := db.GetStats()
	if err != nil {
		t.Fatalf("获取数据库统计失败: %v", err)
	}
	if stats == nil {
		t.Fatal("数据库统计为空")
	}

	// 测试连接池统计
	poolStats := db.GetConnectionPoolStats()
	if poolStats == nil {
		t.Fatal("连接池统计为空")
	}

	// 测试性能优化
	if err := db.OptimizePerformance(); err != nil {
		t.Fatalf("数据库性能优化失败: %v", err)
	}

	// 测试数据库健康检查
	if err := db.Health(); err != nil {
		t.Fatalf("数据库健康检查失败: %v", err)
	}

	// 测试数据库清理
	if err := db.CleanupOldData(30); err != nil {
		t.Fatalf("数据库清理失败: %v", err)
	}

	// 测试数据库备份
	if err := db.Backup("/tmp/test_backup.db"); err != nil {
		t.Fatalf("数据库备份失败: %v", err)
	}

	// 测试数据库维护
	if err := db.Vacuum(); err != nil {
		t.Fatalf("数据库维护失败: %v", err)
	}
}

// TestPreemptiveCoordinatorCoverage 抢占式协调器测试覆盖
func TestPreemptiveCoordinatorCoverage(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	clientConfig := &config.ClientManagementConfig{
		PrepareTimeout: 2 * time.Second,
		TestTimeout:    10 * time.Second,
		StopTimeout:    2 * time.Second,
		RetryAttempts:  2,
		RetryDelay:     500 * time.Millisecond,
	}

	clientManager := client.NewManager(clientConfig, logger)
	coordinator := coordinator.NewPreemptiveTestCoordinator(clientManager, logger)

	// 测试获取详细指标
	metrics := coordinator.GetDetailedMetrics()
	if metrics == nil {
		t.Fatal("详细指标为空")
	}

	// 测试增强错误处理
	testErr := &config.ValidationError{Field: "test", Message: "test error"}
	coordinator.EnhancedErrorHandling(testErr, "test-client", "test-operation")

	// 测试重试机制
	retryCount := 0
	retryOperation := func() error {
		retryCount++
		if retryCount < 3 {
			return testErr
		}
		return nil
	}

	if err := coordinator.RetryWithBackoff(retryOperation, 3, 100*time.Millisecond); err != nil {
		t.Fatalf("重试机制失败: %v", err)
	}

	// 测试公平抢占算法
	testClients := []client.Client{
		&client.MockClient{ID: "client1"},
		&client.MockClient{ID: "client2"},
	}

	sortedClients := coordinator.FairPreemptionAlgorithm(testClients)
	if len(sortedClients) != len(testClients) {
		t.Fatal("公平抢占算法返回的客户端数量不正确")
	}
}

// TestAPICoverage API测试覆盖
func TestAPICoverage(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	// 创建API服务器配置
	serverConfig := &api.ServerConfig{
		Host:         "localhost",
		Port:         8080,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
		EnableCORS:   true,
		EnableAuth:   false,
		StaticDir:    "web/static",
	}

	// 创建依赖组件
	clientConfig := &config.ClientManagementConfig{
		PrepareTimeout: 2 * time.Second,
		TestTimeout:    10 * time.Second,
		StopTimeout:    2 * time.Second,
		RetryAttempts:  2,
		RetryDelay:     500 * time.Millisecond,
	}

	clientManager := client.NewManager(clientConfig, logger)
	testCoordinator := coordinator.NewTestCoordinator(clientManager, 2, logger)

	scheduleConfig := &config.ScheduleConfig{
		Mode:     "always",
		Timezone: "Asia/Shanghai",
	}

	testScheduler, err := scheduler.NewScheduler(scheduleConfig, testCoordinator, nil, logger)
	if err != nil {
		t.Fatalf("创建调度器失败: %v", err)
	}

	syncConfig := &sync.SyncConfig{
		PeerHost:          "*************",
		PeerPort:          8080,
		SyncInterval:      30 * time.Second,
		HeartbeatInterval: 10 * time.Second,
		ConnectTimeout:    5 * time.Second,
		SyncTimeout:       30 * time.Second,
		RetryAttempts:     3,
		RetryDelay:        2 * time.Second,
		EnableCompression: true,
		EnableEncryption:  false,
	}

	syncManager := sync.NewSyncManager(syncConfig, nil, logger)

	// 创建API服务器
	apiServer := api.NewServer(
		serverConfig,
		clientManager,
		testCoordinator,
		testScheduler,
		syncManager,
		nil,
		logger,
	)

	if apiServer == nil {
		t.Fatal("API服务器创建失败")
	}

	// 测试路由器获取
	router := apiServer.GetRouter()
	if router == nil {
		t.Fatal("获取路由器失败")
	}

	// 测试服务器启动
	go func() {
		if err := apiServer.Start(); err != nil {
			t.Errorf("API服务器启动失败: %v", err)
		}
	}()

	// 等待服务器启动
	time.Sleep(100 * time.Millisecond)

	// 测试服务器停止
	if err := apiServer.Stop(); err != nil {
		t.Fatalf("API服务器停止失败: %v", err)
	}
}

// TestSchedulerCoverage 调度器测试覆盖
func TestSchedulerCoverage(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	clientConfig := &config.ClientManagementConfig{
		PrepareTimeout: 2 * time.Second,
		TestTimeout:    10 * time.Second,
		StopTimeout:    2 * time.Second,
		RetryAttempts:  2,
		RetryDelay:     500 * time.Millisecond,
	}

	clientManager := client.NewManager(clientConfig, logger)
	testCoordinator := coordinator.NewTestCoordinator(clientManager, 2, logger)

	scheduleConfig := &config.ScheduleConfig{
		Mode:     "always",
		Timezone: "Asia/Shanghai",
	}

	scheduler, err := scheduler.NewScheduler(scheduleConfig, testCoordinator, nil, logger)
	if err != nil {
		t.Fatalf("创建调度器失败: %v", err)
	}

	// 测试调度器启动
	if err := scheduler.Start(); err != nil {
		t.Fatalf("调度器启动失败: %v", err)
	}

	// 测试获取调度状态
	status := scheduler.GetStatus()
	if status == nil {
		t.Fatal("调度状态为空")
	}

	// 测试调度器停止
	if err := scheduler.Stop(); err != nil {
		t.Fatalf("调度器停止失败: %v", err)
	}
}

// TestSyncCoverage 同步模块测试覆盖
func TestSyncCoverage(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	syncConfig := &sync.SyncConfig{
		PeerHost:          "*************",
		PeerPort:          8080,
		SyncInterval:      30 * time.Second,
		HeartbeatInterval: 10 * time.Second,
		ConnectTimeout:    5 * time.Second,
		SyncTimeout:       30 * time.Second,
		RetryAttempts:     3,
		RetryDelay:        2 * time.Second,
		EnableCompression: true,
		EnableEncryption:  false,
	}

	syncManager := sync.NewSyncManager(syncConfig, nil, logger)

	// 测试同步管理器启动
	if err := syncManager.Start(); err != nil {
		t.Fatalf("同步管理器启动失败: %v", err)
	}

	// 测试获取同步状态
	status := syncManager.GetStatus()
	if status == nil {
		t.Fatal("同步状态为空")
	}

	// 测试同步管理器停止
	if err := syncManager.Stop(); err != nil {
		t.Fatalf("同步管理器停止失败: %v", err)
	}
}

// BenchmarkDatabasePerformance 数据库性能基准测试
func BenchmarkDatabasePerformance(b *testing.B) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	dbConfig := database.DefaultConfig()
	dbConfig.Path = ":memory:"

	db, err := database.New(dbConfig, logger)
	if err != nil {
		b.Fatalf("创建数据库失败: %v", err)
	}
	defer db.Close()

	if err := db.Migrate(); err != nil {
		b.Fatalf("数据库迁移失败: %v", err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := db.GetStats()
		if err != nil {
			b.Fatalf("获取数据库统计失败: %v", err)
		}
	}
}

// BenchmarkPreemptiveCoordinator 抢占式协调器性能基准测试
func BenchmarkPreemptiveCoordinator(b *testing.B) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	clientConfig := &config.ClientManagementConfig{
		PrepareTimeout: 2 * time.Second,
		TestTimeout:    10 * time.Second,
		StopTimeout:    2 * time.Second,
		RetryAttempts:  2,
		RetryDelay:     500 * time.Millisecond,
	}

	clientManager := client.NewManager(clientConfig, logger)
	coordinator := coordinator.NewPreemptiveTestCoordinator(clientManager, logger)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		coordinator.GetDetailedMetrics()
	}
}
